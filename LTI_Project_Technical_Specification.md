# LTI Student Observer & Calendar Viewer - Technical Specification

## Project Overview

This document outlines the technical approach for building a Canvas LTI 1.3 application that enables temporary observer/observee relationships and calendar viewing functionality. The application will be deployed across 4 Canvas instances (1 parent consortium + 3 child instances) with shared data synchronization.

## Technology Stack

### Backend
- **Rails 7**: Modern Ruby on Rails framework
- **PandaPal**: LTI 1.3 framework for Canvas integration
- **CanvasSync**: Gem for Canvas data synchronization
- **Sidekiq**: Background job processing with Redis
- **PostgreSQL**: Primary database for application data
- **Redis**: Caching and job queue management

### Frontend
- **React 18.3.1**: Modern React with hooks and functional components
- **Shakapacker**: Webpack integration for Rails
- **Instructure UI**: Official Canvas design system components
- **React Router**: Client-side routing

## Architecture Overview

### LTI 1.3 Integration
The application implements full LTI 1.3 compliance including:
- OIDC (OpenID Connect) authentication flow
- JWT (JSON Web Token) validation and processing
- Deep linking support for flexible placement
- Canvas developer key configuration

### Multi-Instance Data Synchronization
- Shared PostgreSQL database accessible by all instances
- Real-time synchronization of observer link data
- Conflict resolution for concurrent operations
- Instance-specific configuration management

### Canvas API Integration
The application integrates with multiple Canvas API endpoints:

#### Authentication APIs
- `/api/lti/authorize_redirect` - OIDC login initiation
- `/login/oauth2/token` - Token exchange for API access

#### User & Enrollment APIs
- `GET /api/v1/courses/:course_id/enrollments` - Course student lists
- `GET /api/v1/accounts/:account_id/users` - Account-level student lists
- `GET /api/v1/users/:user_id` - Individual user details

#### Observer Management APIs
- `POST /api/v1/users/:user_id/observees` - Create observer links
- `DELETE /api/v1/users/:user_id/observees/:observee_id` - Remove links
- `GET /api/v1/users/:user_id/observees` - List existing links

#### Calendar APIs
- `GET /api/v1/calendar_events` - General calendar events
- `GET /api/v1/users/:user_id/calendar_events` - User-specific events
- `GET /api/v1/courses/:course_id/calendar_events` - Course calendar events

## Database Schema Design

### Core Tables

#### observer_links
```sql
CREATE TABLE observer_links (
  id BIGSERIAL PRIMARY KEY,
  observer_canvas_id BIGINT NOT NULL,
  observee_canvas_id BIGINT NOT NULL,
  canvas_link_id BIGINT,
  created_at TIMESTAMP NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  renewed_at TIMESTAMP,
  renewal_count INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active',
  instance_id VARCHAR(50),
  created_by_user_id BIGINT,
  INDEX idx_observer_active (observer_canvas_id, status),
  INDEX idx_expires_at (expires_at),
  INDEX idx_instance_sync (instance_id, created_at)
);
```

#### canvas_users (Cache Table)
```sql
CREATE TABLE canvas_users (
  id BIGSERIAL PRIMARY KEY,
  canvas_id BIGINT UNIQUE NOT NULL,
  name VARCHAR(255),
  sortable_name VARCHAR(255),
  sis_user_id VARCHAR(255),
  email VARCHAR(255),
  last_synced_at TIMESTAMP,
  INDEX idx_canvas_id (canvas_id),
  INDEX idx_sortable_name (sortable_name),
  INDEX idx_sis_user_id (sis_user_id)
);
```

#### calendar_preferences
```sql
CREATE TABLE calendar_preferences (
  id BIGSERIAL PRIMARY KEY,
  observer_link_id BIGINT REFERENCES observer_links(id),
  calendar_type VARCHAR(50), -- 'personal', 'course', 'group'
  calendar_id BIGINT,
  visible BOOLEAN DEFAULT true,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
);
```

## Key Features Implementation

### 1. Temporary Observer Links (12 hours)

**Core Functionality:**
- Create temporary Canvas observer/observee relationships
- 1-hour automatic expiration with optional renewal
- Multi-instance synchronization
- Manual termination capability

**Technical Implementation:**
- Canvas API integration for link creation/deletion
- Background job for automatic expiration cleanup
- Real-time status updates across instances
- Conflict resolution for concurrent operations

### 2. Student List Management (8 hours)

**Course Level Access:**
- Paginated student lists (50 per page)
- Alphabetical sorting by Canvas sortable name
- SIS ID display
- Role-based access control

**Account Level Access:**
- Sub-account student aggregation
- Search functionality (3+ character minimum)
- Hierarchical account support
- Performance optimization for large datasets

### 3. Calendar Integration (12 hours)

**Calendar Data Management:**
- Personal calendar access
- Course calendar integration (up to 9 calendars)
- Group calendar support
- Read-only implementation

**Display Logic:**
- Default visibility rules (9 or fewer: all visible, 10+: personal only)
- User-controlled calendar toggling
- Canvas-compatible UI design
- Responsive layout support

### 4. Background Processing (9 hours)

**Data Synchronization:**
- 6-hour Canvas data sync cycles
- Incremental update processing
- Error recovery and retry logic
- Performance monitoring

**Link Management:**
- Automatic expiration processing
- Cleanup of orphaned records
- Cross-instance synchronization
- Audit trail maintenance

## Security Considerations

### Authentication & Authorization
- LTI 1.3 JWT validation
- Canvas API token management
- Role-based access control
- Session security

### Data Protection
- Encrypted sensitive data storage
- Secure API communication (HTTPS)
- Canvas data privacy compliance
- Audit logging for observer activities

## Performance Optimization

### Caching Strategy
- Redis for Canvas API response caching
- Database query optimization
- Pagination for large datasets
- Lazy loading for calendar data

### Database Optimization
- Strategic indexing for common queries
- Connection pooling
- Query optimization
- Background job queue management

## Deployment Architecture

### Multi-Instance Setup
- Shared database configuration
- Instance-specific environment variables
- Load balancing considerations
- SSL certificate management

### Canvas Integration
- Developer key configuration per instance
- Tool placement settings
- Permission verification
- Launch point configuration

## Time Estimation Breakdown

| Phase | Hours | Justification |
|-------|-------|---------------|
| Foundation & Setup | 18 | LTI 1.3 setup, Rails/React configuration, database design |
| Core Observer Functionality | 24 | Student lists, observer links, multi-instance sync |
| Calendar Integration | 12 | Canvas calendar API, UI components, filtering logic |
| Background Jobs | 9 | Sidekiq setup, sync jobs, cleanup processes |
| UI/UX Implementation | 11 | Instructure UI integration, responsive design |
| Testing & Validation | 8 | API testing, frontend testing, end-to-end validation |
| Deployment & Documentation | 6 | Environment setup, Canvas installation, documentation |
| **Total** | **88** | **Includes 10% buffer for unexpected issues** |

## Risk Mitigation

### Technical Risks
- Canvas API rate limiting: Implement caching and request throttling
- Multi-instance synchronization: Use database-level locking and conflict resolution
- LTI 1.3 complexity: Leverage PandaPal gem expertise and Canvas documentation

### Timeline Risks
- Complex Canvas API integration: Allocate extra time for API testing
- Multi-instance deployment: Plan for environment-specific configuration issues
- UI/UX refinement: Use Instructure UI components to reduce custom styling

## Success Criteria

1. **Functional Requirements Met**: All SSD requirements implemented and tested
2. **Performance Standards**: Sub-2-second response times for all user interactions
3. **Security Compliance**: LTI 1.3 security standards and Canvas data privacy
4. **Multi-Instance Compatibility**: Seamless operation across all 4 Canvas instances
5. **User Experience**: Intuitive interface following Canvas design patterns

This technical specification provides a comprehensive roadmap for delivering a robust, scalable LTI application within the 80-hour timeframe while maintaining high quality and security standards.
