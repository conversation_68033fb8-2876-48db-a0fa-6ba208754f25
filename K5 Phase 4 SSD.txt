﻿SCHEDULE 1
Milestones and Deliverables
Milestone 1: Design Solution
USER STORIES INCLUDED
	1. As a user, I can access a user interface so that I can interact with data.
	SUMMARY
	Creation of and Customer approval for the finalized user interface design mockups for the K5 Dashboards including:
* K5 Dashboard Student Theme selection
* K5 Dashboard Theme displays
* K5 Dashboard Completion Fireworks
* K5 Dashboard Student Enable/Disable Audio Setting
* K5 Dashboard New User Tool Tips
* K5 Dashboard Teacher Contact Information
* K5 Dashboard Announcement Views


Note: Solution designs proposed and accepted in this milestone supersede any other mockups, including wireframes in this SSD (see Limitations & Exclusions #3).
Customer Requirements
1. Within 10 Business Days from the Effective Date of this agreement (see Limitations & Exclusions #4), provide to Instructure the art assets (see Limitations & Exclusions #8) for the three K5 Student Dashboard Visual Themes, Forest, Space, and Winter.  Including:
   1. Mascot and Mascot Island assets for each theme.
      1. Including Waving Strider Mascot, Helpful Strider Mascot, and Postal Strider Mascot.
   2. Environment assets for each theme.
   3. Daily Plan Completion assets for each theme.
   4. Art assets must be provided within JPEG (JPG), PNG, or SVG formatted files.
2. Within 10 Business Days from the Effective Date of this agreement (see Limitations & Exclusions #6), provide to Instructure the art and audio assets (see Limitations & Exclusions #8) for K5 Student Dashboard completion fireworks.
   1. Completion Firework Graphics files must be provided as MP4, GIF, or PNG formatted files.
   2. Completion Firework Audio files must be provided as MP3 formatted files.
3. Provide a primary stakeholder to review and approve mockup designs.
4. Provide any feedback needed to complete the mockup.
5. UAT consists of review and approval of final draft of UI mockups.
Instructure Requirements
1. Provide a UI/UX design resource.
2. Create visual mock-ups for the following deliverables, which reflect the requirements in the remaining milestones.
   1. K5 Dashboard Student Theme Selection (see Milestone 3)
   2. K5 Dashboard Theme displays (see Milestone 3)
   3. K5 Dashboard Student Enable/Disable Audio Setting (see Milestone 4)
   4. K5 Dashboard Completion Fireworks on Module and Daily Plan views (see Milestone 4)
   5. K5 Dashboard New User Tool Tips (see Milestone 5)
   6. K5 Dashboard Teacher Contact Information views (see Milestone 5)
   7. K5 Dashboard Announcement view (see Milestone 5)
3. Present mock-ups for review and iterate once (if necessary) to integrate customer feedback.


________________
Milestone 2: Prevent K5 Dashboard Redirects on Parent Consortium Instance
USER STORIES INCLUDED
	2. As a K5 Student Dashboard User, I am not redirected into the K5 Dashboard application when I access the Parent Consortium Instance, so that I am instead sent to my home instance via a separate process.
	SUMMARY
	The CSS/JS Overrides that facilitate the navigation of K5 Users and their Learning Coaches to the K5 Dashboards Application will be modified so that these instant redirects do not occur when users access the Parent Consortium Instance.


Instructure Requirements
1. Make changes to the existing CSS/JS Overrides for the K5 Dashboard to disable the automatic redirection of K5 Student users and K5 Student Learning Coaches to the K5 Student Dashboard application when the users are accessing the Parent Consortium Instance, via either the Instructure or Vanity URL.  Automatic redirection of K5 Student users and K5 Student Learning Coaches will continue to take place on all other instances.
   1. Parent Consortium Instance: https://k12learning.instructure.com/
      1. Vanity URL: https://learn0.k12.com/
   2. Provide the updated CSS/JavaScript Override code to Customer for deployment.
Customer Requirements
1. Once the updated CSS/JS Overrides are received from Instructure (see Instructure Requirement 1.2), deploy them to Canvas by doing the following:
   1. Combine the updated JavaScript and CSS provided by Instructure with any non K5 Dashboards related JavaScript and/or CSS overrides used in Canvas.
   2. Upload the combined JavaScript and CSS overrides in the Canvas theme editor and apply it at the root account level.


Milestone 3: K5 Dashboards Visual Themes
USER STORIES INCLUDED
	3. As a K5 Student User, I can select the K5 visual theme I desire from within my settings, so that I can customize my K5 Student Dashboards experience.
4. As a K5 Student User, I can select a ‘No Theme’ visual theme from within my settings, so that I can have the themeless view of the K5 Student Dashboards if I desire.
5. As a K5 Student User, if I select a visual theme, I see that theme represented throughout the K5 Dashboards, so that I am engaged with my learning.
	SUMMARY
	K5 Student Users will be able to select from 3 visual themes and one non-visual theme within their Dashboard settings.  When a visual theme is selected, that theme will be displayed for the student through the K5 Student Dashboards.  When the non-visual theme is selected, the current views will be displayed.
Visual themes include unique mascot, environment, and completion assets.  Visual themes will be anchored to the bottom of the page.  Mascots and environments will float on Daily Agenda, Past Due Items, Resources, and Announcement and Teacher Contact Information views.
Visual themes will be displayed on the Courses Dashboard, Individual Course views, Agenda views (including Daily and Weekly plans and “Past Due” items), “Resources” view, and “Announcements and Teacher Contact Information” views.


Customer Requirements
1. Prior to the start of development, Provide Instructure with the desired Strider the Fox mascot and dialog text to display on Announcements and Teacher Contact Information pages.
   1. Provide the desired texts for the following scenarios:
      1. For Teacher Contact Information:
         1. List of Teachers View
         2. Individual Teacher Contact Information View
      2. For Announcements:
         1. Existing Un-dismissed Global Announcements
         2. No Un-dismissed Global Announcements
Instructure Requirements
1. Add functionality to the K5 Student Dashboards Application that allows for K5 Student Users to select from one of three visual themes or one ‘No Theme’ options.
   1. The visual themes will be: “Forest”, “Winter”, and “Space”.  By default all K5 Student Users will have the setting set to “Forest”.
   2. The selection of Themes will be located within the Student’s K5 Student Dashboard Settings.
2. Add functionality to the K5 Student Dashboards Application that adds the visual elements for the students’ selected visual theme onto their K5 Student Dashboard Views.
   1. Theme elements art asset files will be provided by Customer (see Milestone 1: Customer Requirement 1).
   2. Theme elements will consist of the following assets, each unique for a given theme:
      1. Waving Strider the Fox Mascot Art and Strider’s Island Art
      2. Helpful Strider the Fox Mascot Art and Strider’s Island Art
      3. Postal Strider the Fox Mascot Art and Strider’s Island Art
      4. Environment Art
      5. Background Art
      6. Daily Plan Completion Art
   3. The selected themes will display various elements in the following locations:
      1. Courses Dashboard View
         1. Environment Art and Background Art
      2. Individual Course Views
         1. Environment Art and Background Art
      3. Agenda Views, including Daily Plan, Weekly Plan, and Past Due views
         1. Daily Plan:
            1. If Student has Remaining (to-be-completed) Daily Agenda Items for the selected day: Environment Art, Background Art, Waving Strider the Fox Mascot Art and Strider’s Island Art.
               1. Waving Strider the Fox will have a dialog bubble with the following: “Hi *Student’s Name*!”, where *Student’s Name* will be replaced by the Student’s first name.
            2. If Student has no remaining (to-be-completed) Daily Agenda Items for the selected day: Environment Art, Background Art, Waving Strider the Fox Mascot Art and Strider’s Island Art, and Daily Plan Completion Art.
               1. Waving Strider the Fox will have a dialog bubble with the following: “Great work!  You finished all of today’s lessons”.
               2. Daily Plan Completion Art will sit to the right of the Daily Plan Progress Bar, and will replace 100%.
         2. Weekly Plan:  Environment Art and Background Art
         3. Past Due:
            1. If Student has Past Due Items: Environment Art, Background Art, and Helpful Strider the Fox Mascot Art and Strider’s Island Art.
               1. Helpful Strider the Fox will have a dialog bubble with the following: “Contact your teacher for help catching up.”
            2. If Student does not have Past Due Items: Environment Art, Background Art, Waving Strider the Fox Mascot Art and Strider’s Island Art
               1. Waving Strider the Fox will have a dialog bubble with the following: “Great work!  You have no Past Due items!”
      4. Resources View
         1. Environment Art and Background Art
      5. Announcements and Teacher Contact Information View[4] (see Milestone 5)
         1. Environment Art, Background Art, and Postal Strider the Fox Mascot Art and Strider’s Island Art.
            1. On the Teacher Contact Information List View, Postal Strider the Fox will have a dialog bubble with the text as provided by Customer (see Customer Requirement 1.1.1.1).
            2. On the Individual Teacher Contact Information View, Postal Strider the Fox will have a dialog bubble with the text as provided by Customer (see Customer Requirement 1.1.1.2).
            3. On the Announcement Tab, if the user has un-dismissed Announcements, Postal Strider the Fox will have a dialog bubble with the text as provided by Customer (see Customer Requirement 1.1.2.1).
            4. On the Announcement Tab, if the user has no un-dismissed Announcements, Postal Strider the Fox will have a dialog bubble with the text as provided by Customer (see Customer Requirement 1.1.2.2).
   4. Background Art will be anchored to the bottom of K5 Student Dashboard pages in which it is shown.
   5. Strider the Fox Mascot (Waving, Helpful, and Postal versions), Strider’s Island, and Environment Art will be set to float on pages in which they are shown.
3. If a student has selected the “No Theme” option, show them the current theme-less view of the K5 Student Dashboards.
   1. K5 Student Learning Coaches will be shown the “No Theme” view of the K5 Student Dashboards.
________________
Milestone 4: K5 Dashboards Completion Fireworks
USER STORIES INCLUDED
	   6. As a K5 Student User, I can enable or disable the Completion Fireworks audio, so that I can customize my K5 Student Dashboards experience.
   7. As a K5 Student User, when I complete all Module Requirement Items within a Module, I am presented with celebratory completion firework effects within the K5 Student Dashboards, so that I am rewarded for completing a module.
   8. As a K5 Student User, when I complete all items on my daily plan, I am presented with celebratory completion firework effects within the K5 Student Dashboards, so that I am rewarded for completing all daily plan items.
   9. As a K5 Student User, when I complete all items on my daily plan and have been presented the celebratory completion firework effects, I am navigated to the Past Due view within the K5 Student Dashboards if I have Past Due items, so that I am encouraged to complete missing work.
	SUMMARY
	K5 Student Users will be able to enable/disable K5 Dashboard Completion Fireworks audio within their dashboard settings.
K5 Student Users will be presented with celebratory Completion Fireworks visuals, and if enabled, audio when they access their Individual Course view after completing all Module Requirement Items within a module.
K5 Student Users will be presented with celebratory Completion Fireworks visuals, and if enabled, audio when they access their Agenda’s Daily Plan view after completing all agenda items for the current day.  If the student has past due items, they will then be brought to the Past Due items view.


Customer Requirements
   1. Prior to the start of development, Provide Instructure with the desired dialog text to include with Celebratory Completion Fireworks Graphics and Audio for the following:
   1. The student completed one or more Modules, by completing all of the required module items for the module(s).
   2. The student completed all Daily Plan items for the current day, and does not have any past due items.
   3. The student completed all Daily Plan items for the current day, and has past due items.
Instructure Requirements
   1. Add functionality to the K5 Student Dashboards Application that allows for K5 Student Users to enable or disable K5 Dashboard Completion Firework Audio.  By default, the setting will be enabled.
   1. The option will be located within the Student’s K5 Student Dashboard Settings.  When disabled, Celebratory Completion Fireworks will display but will not be accompanied by their audio.
   2. Add functionality to the K5 Student Dashboards Application that displays graphics and triggers an audio (if enabled) event (see Milestone 1, Customer Requirement 2) when students complete all Module Item Requirements or All Daily Plan items.  Celebratory Completion Fireworks graphics will trigger in all selected themes, including the “No Theme” option.
   1. When students have completed all module requirement items for one or more Modules within a course, when the student navigates to the individual course view for the course, trigger the celebratory fireworks graphics and audio and display the Customer-provided text (see Customer Requirement 1.1).
   1. Once the fireworks have been displayed for completed modules, they will not be set to display again in the same course until the student has completed another module.
   2. When students have completed all of the current day’s daily plan items, and are accessing the daily plan for the first time (or when they completed items on the daily plan view itself), trigger the celebratory fireworks graphics and audio and display the Customer-provided text.
   1. If the student does not have any items within their Past Due tab, display the dialogue from Customer Requirement 1.2.
   2. If the student has items within their Past Due tab, display the dialogue from Customer Requirement 1.3, and then after the student acknowledges the dialogue, direct them to their Past Due items view.
   3. Once the fireworks have been displayed for a completed daily plan for a given calendar day, they will not be set to display again on the same day.
________________
Milestone 5: K5 Dashboards Tool Tips and Teacher Contact Information
USER STORIES INCLUDED
	      10. As a K5 Student User, if I don’t have any currently active courses when I view the K5 Dashboards Landing Page, I am provided with an informative tool tip, so that I understand the view I have and why it is currently empty.
      11. As a K5 Student User, when I view the K5 Dashboards Landing Page, I am provided with a dismissable notification settings informative tool tip, so that I understand I have the ability to set my notification preferences in Canvas.
      12. As a K5 Student User, within the K5 Dashboards, I can see my teachers’ contact information, so that I can reach out to them as needed.
      13. As a K5 Student Learning Coach, within the K5 Dashboards, I can see my student’s teachers’ contact information, so that I can reach out to them as needed.
      14. As a K5 Student Dashboards User, within the K5 Dashboards, I can see my Canvas Global Announcements, so that I can access, read, and dismiss them.
	SUMMARY
	K5 Student Users that do not currently have any active courses, will be presented an informative tool tip on their Courses Dashboard page explaining why the page is empty.
K5 Student Users, will be presented a dismissable informative tool tip on their Courses Dashboard page regarding Canvas notifications settings.
K5 Student Users and K5 Student Learning Coaches will be able to access their Teachers’ contact information via views added to the K5 Student Dashboards.
K5 Student Users and K5 Student Learning Coaches will be able to access and dismiss their Canvas Global Announcements via views added to the K5 Student Dashboards.


Customer Requirements
      1. Prior to the start of development, provide the following:
      1. The desired tool tip text to display when a student does not currently have any active or accessible courses in Canvas.
      2. The desired text to display when a user does not have any un-dismissed Global Announcements.
Instructure Requirements
      1. Add functionality to the K5 Student Dashboards Application that displays a tool tip for all K5 Student Users that have no currently active courses when they access the Course Dashboard view of the K5 Student Dashboard.
      1. The Tool Tip will only be displayed when the student has no courses showing within the Courses Dashboard.
      2. The Tool Tip will display the text as provided in Customer Requirement 1.1).
      3. The Tool Tip cannot be dismissed.
      4. Once the student has at least one active course, that is a course that shows within the K5 Student Dashboard’s Courses Dashboard, cease displaying the tool tip.
      2. Add functionality to the K5 Student Dashboards Application that displays a dismissible tool tip for all K5 Student Users when they access the Courses Dashboard view.
      1. The tool tip can be dismissed/removed.
      2. The tool tip will display the following text: “Notifications.  Tell us how and when you would like to be notified of events in Canvas.” and will provide a button that will link to the user’s Canvas Notification Preferences screen.
      3. The Tool Tip will be shown to all K5 Student Dashboard users and will continue being displayed until it is dismissed.  Once the tool tip has been dismissed it cannot be recovered (see Limitations & Exclusions #9).
      3. Add a navigation option within the K5 Student Dashboards Application that provides users with access to a list of their Teachers, along with individual teacher Contact Information, and their Global Announcements.
      1. For K5 Students, the Teacher Contact Information view will start with a list of all their Teachers.
      1. The list will comprise all Teacher users in Canvas, that is users with active teacher roles (or active custom roles based on the teacher role) in each of the student’s active courses.  Active courses are those courses for which a course tile is generated on the Courses Dashboard of the K5 Student Dashboards.  For K5 Student Learning Coaches, this list will be the list as shown to their only or currently selected student.
      2. Teachers in this list will be sorted alphabetically by Canvas Sortable Name.
      3. Each Teacher in the list will be rendered as a link to that teacher’s individual contact information page.
      1. Individual Teacher Contact Information Pages will include the teacher’s Canvas Sortable Name, the Teacher’s Canvas registered email address(es), and the Teacher’s Canvas registered phone number(s).
      1. Teachers for whom no Canvas registered phone number exists will not have a phone number displayed.
      2. Teachers’ email addresses will be rendered as mailto URIs.
      3. Teachers for whom more than one email address or phone number have been registered within Canvas will have all such contact methods displayed, in the same order as retrieved from Canvas, with Email addresses shown first, and Phone Numbers displayed after.
      4. Teachers’ individual contact information pages will allow for users to navigate back to the list of all teachers.
      2. The Global Announcements view will display all of the user’s un-dismissed Global Canvas Announcements, if any exist.  The view will also provide users with a link to access the Global Announcements view in Canvas, outside of the K5 Student Dashboards.
      1. Global Announcements are announcements created by an administrator user at the account and sub-account levels.  Global Announcements are displayed on the Canvas Dashboards for all users to whom the  announcement is targeted as well as within the users’ Global Announcements view in Canvas.
      2. Allow for Global Announcements to be dismissed from within the Global Announcements view of the K5 Student Dashboards.  Once dismissed, the Global Announcement will no longer be available to the user within the K5 Student Dashboards.  These dismissed announcements can still be viewed within the Global Announcements view, accessible via the link provided to users.
      3. Only Global Announcements will be displayed within this section.  Course announcements will be accessible to users within their Canvas courses.
      4. If the user has no un-dismissed Global Announcements, display the Customer-provided text (see Customer Requirement 1.2).
________________
SCHEDULE 2
User Access
The following table defines user access to the application. Access definition for course roles (i.e. teacher, TA, designer, student, observer) include all custom roles created in Canvas that are based on the standard Canvas role. Administrator roles in Canvas are treated equally for external tool access, but not for external tool functionality.

Launch Points designated with an asterisk denote launch points for which the user can access, but for which only a notification message, and no functionality, will be provided.
SOLUTION ROLE
	CANVAS ROLE
	CAN ACCESS
	LAUNCH POINT
	Stride Administrator
	Root Account Administrator
	Yes
	Account Navigation (Root Account)
Account Navigation (Sub-Account)
*Course Launch
	School Administrator
	Sub-Account Administrator
	Yes
	Account Navigation (Sub-Account)
*Course Launch
	N/A
	Teacher
	No
	*Course Launch
	K5 Learning Coach
	Account-Linked Observer[5]
	Yes
	CSS/JS Facilitated Launch
Course Launch
	Non K5 Learning Coach
	Account-Linked Observer[6]
	No
	*Course Launch[7]
	N/A
	TA
	No
	*Course Launch
	N/A
	Designer
	No
	*Course Launch
	K5 Student
	Student[8]
	Yes
	CSS/JS Facilitated Launch
Course Launch
	Non K5 Student
	Student[9]
	No
	*Course Launch6
	Un-assigned User
	Un-assigned/Un-enrolled User
	No
	N/A


Version: 2025.06.11
________________
[1] Number of estimated person-hours required to complete the requirements outlined in this SSD.
[2] Estimated cost at the hourly rate defined in Customer’s Statement of Work – Retainer Agreement. Final cost may be higher or lower, depending on the number of actual hours required to deliver the Solution. Excludes hosting, maintenance, and support fees, if applicable; see Fees for full pricing.
[3] Base estimate, after development begins, of business days to complete all project development work and testing; see Development Timeline for details.
[4] Announcements and Teacher Contact Information views are being added during Milestone 4.
[5] User with at least one Account-Linked Observer relationship to a K5 Student user.
[6] User with no Account-Linked Observer relationship to a K5 Student user.
[7] By default, hidden from view by Solution.
[8] User with an active Student Enrollment in Canvas for whom the SIS-sourced Student Grade Level is Kindergarten, 1st, 2nd, 3rd, 4th, or 5th Grade.
[9] User with an active Student Enrollment in Canvas for whom the SIS-sourced Student Grade Level 6th, 7th, 8th, 9th, 10th, 11th, or 12th Grade.
