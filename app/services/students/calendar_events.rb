# frozen_string_literal: true

module Students
  class CalendarEvents
    # Fetch unique calendar events for a user across all shards, grouped by date
    def self.fetch_events_for_user(user, due_date, weekly: false)
      return {} unless due_date

      date_range = build_date_range(due_date, weekly)
      all_events = collect_events_from_shards(user, date_range)
      all_events.uniq! { |e| e[:sharded_canvas_id] }
      all_events.group_by { |event| event[:start_at].strftime('%Y-%m-%d') }
    end

    # Mark a calendar event as complete for a user across all shards
    def self.mark_event_complete(user, event_id, organization_name, with_organization_proc, current_organization_proc)
      with_organization_proc.call(organization_name) do
        event = CalendarEvent.new(canvas_id: event_id)

        CompletedCalendarEvent.find_or_create_by!(canvas_user_id: user.canvas_id, canvas_id: event.canvas_id)

        # If the event belongs to a different organization than the current one,
        # we need to switch to that organization's tenant to update the event there
        current_org = current_organization_proc.call
        unless event.home_organization.name == current_org.name
          event.home_organization.switch_tenant do
            CompletedCalendarEvent.find_or_create_by!(canvas_user_id: user.primary_record.canvas_id, canvas_id: event.primary_record.canvas_id)
          end
        end

        # Calendar events are duplicated across shards for cross-shard accessibility
        # We need to update the completion status on all shards for consistency
        # This iterates through all the user's shards and updates the event on each one
        user.against_shards do |shard_user|
          CompletedCalendarEvent.find_or_create_by!(canvas_user_id: shard_user.canvas_id, canvas_id: event.sharded_canvas_id)
        end
      end
    end

    def self.build_date_range(due_date, weekly)
      date = Time.zone.parse(due_date)

      if weekly
        start_date = date.beginning_of_week
        start_date.beginning_of_day..(start_date + 4.days).end_of_day
      else
        date.all_day
      end
    end

    def self.collect_events_from_shards(user, date_range)
      all_events = []

      user.against_shards do |shard_user, org|
        events = shard_user.canvas_calendar_events(date_range)
        completed_events = fetch_completed_events(shard_user, events)
        all_events += build_event_data(events, completed_events, shard_user, org)
      end

      all_events
    end

    def self.fetch_completed_events(shard_user, events)
      CompletedCalendarEvent
        .where(canvas_user_id: shard_user.canvas_id, canvas_id: events.map(&:canvas_id))
        .index_by(&:canvas_id)
    end

    def self.build_event_data(events, completed_events, shard_user, org)
      events.map do |event|
        completed = completed_events[event.canvas_id]

        {
          canvas_id: event.canvas_id.to_s,
          title: event.title,
          start_at: event.start_at,
          end_at: event.end_at,
          completed_at: completed&.created_at,
          html_url: event.html_url,
          html_path: event.html_path(org),
          canvas_user_id: shard_user.canvas_id,
          sharded_canvas_id: event.sharded_canvas_id.to_s,
          organization_name: org.name
        }
      end
    end
  end
end
