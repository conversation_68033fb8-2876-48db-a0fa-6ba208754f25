# frozen_string_literal: true

module Students
  class CelebrationEvents
    # Create or find a daily plan celebration event for all user shards
    def self.create_daily_event(user, due_date)
      user.against_shards do |u|
        CelebrationEvent.find_or_create_by!(
          canvas_user_id: u.canvas_id,
          event_type: 'daily_plan',
          event_date: Time.zone.parse(due_date)
        )
      end
    end

    # Returns true if the daily plan celebration event has been shown on any shard
    def self.daily_event_shown?(user, due_date)
      user.against_shards do |u|
        event = CelebrationEvent.find_by(
          canvas_user_id: u.canvas_id,
          event_type: 'daily_plan',
          event_date: Time.zone.parse(due_date)
        )
        return true if event&.shown_at.present?
      end

      false
    end

    # Mark all pending daily plan celebration events as shown for all shards
    def self.mark_daily_event_shown(user, due_date)
      user.against_shards do |u|
        CelebrationEvent.where(
          canvas_user_id: u.canvas_id,
          event_type: 'daily_plan',
          event_date: Time.zone.parse(due_date),
          shown_at: nil
        ).update_all(shown_at: Time.current)
      end
    end

    #  Mark all pending module completed celebration events as shown for all shards
    def self.mark_module_event_shown(user, course_id)
      user.against_shards do |u|
        CelebrationEvent.where(
          canvas_user_id: u.canvas_id,
          event_type: 'module_completed',
          canvas_course_id: course_id,
          shown_at: nil
        ).update_all(shown_at: Time.current)
      end
    end
  end
end
