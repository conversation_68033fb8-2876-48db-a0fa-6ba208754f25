# frozen_string_literal: true

module QueryBuilder
  class StudentCourseProgress < BaseQueryBuilder
    def self.from_valid_shard(student, options = {})
      raise ArgumentError, 'student is required' if student.blank?
      raise ArgumentError, 'course_shard_id is required' if options[:course_shard_id].blank?

      course_shard_id = options[:course_shard_id].to_s
      course_progress_data = {}
      request_org = current_organization
      student.against_shards do |shard_student|
        next unless current_organization.canvas_shard_id.to_s == course_shard_id

        course_progress_data = StudentCourseProgress.new({
                                                           canvas_user_id: shard_student.canvas_id,
                                                           canvas_course_id: options[:canvas_course_id],
                                                           filters: options[:filters],
                                                           organization: current_organization,
                                                           request_org: request_org
                                                         }).records
      end
      course_progress_data
    end

    def records
      @user = User.find_by(canvas_id: user_id)
      @course = Course.find_by(canvas_id: course_id)
      course_details = course_details_json
      # Create celebration events for completed modules
      create_module_celebration_events(course_details[:modules])
      course_details.merge!(show_fireworks: pending_module_celebration_event?)
      course_details
    end

    def pending_module_celebration_event?
      CelebrationEvent.exists?(
        canvas_user_id: @user.canvas_id,
        canvas_course_id: @course.canvas_id,
        event_type: 'module_completed',
        shown_at: nil
      )
    end

    def course_details_json
      course_progress = Course.where(canvas_id: course_id).joins(sql_joins([course_id])).select(sql_selects).first
      {
        id: @course.id,
        canvas_id: @course.canvas_id,
        name: @course.name,
        course_code: @course.course_code,
        course_sis_id: @course.sis_id,
        current_score: course_progress&.current_score,
        current_letter_grade: course_progress&.current_letter_grade,
        requirement_count: course_progress&.requirement_count,
        requirement_completed_count: course_progress&.requirement_completed_count,
        past_due_requirements_count: course_progress&.past_due_requirements_count,
        requirements_with_due_date_count: course_progress&.requirements_with_due_date_count,
        course_progress_percent: calculate_course_progress_percent(course_progress),
        expected_course_progress: calculate_expected_course_progress(course_progress),
        expected_course_progress_percent: calculate_expected_course_progress_percent(course_progress),
        color_code: fetch_color_code(course_progress&.current_score),
        modules: course_module_items
      }
    end

    def course_module_items
      modules = fetch_active_modules
      preloaded_data = preload_module_data(modules)

      build_module_data(modules, preloaded_data)
    end

    # Determines if a submission should be filtered out based on its status and the active filters
    # If a filter is in the filters array, we should filter out items of that type
    def filtered_out?(submission)
      return false if filters.blank?

      (filters.include?('master') && submission.master?) ||
        (filters.include?('not_master') && submission.not_master?) ||
        (filters.include?('not_completed') && submission.not_completed?) ||
        (filters.include?('not_completed_past_due') && submission.not_completed_past_due?)
    end

    # Determines if a module item should be filtered out based on its status and the active filters
    # Works for both assignment and non-assignment items
    def filtered_out_item?(submission, req_status)
      return false if filters.blank?

      # For assignment items, use the existing submission-based filtering
      return filtered_out?(submission) if submission

      # For non-assignment items, filter based on requirement status
      case req_status
      when 'mastered'
        filters.include?('master')
      when 'not mastered'
        filters.include?('not_master')
      when 'past due'
        filters.include?('not_completed_past_due')
      when 'not completed'
        filters.include?('not_completed')
      when 'completed'
        filters.include?('completed')
      else
        false
      end
    end

    def build_item_json(item, progression, req_status, submission: nil, assignment: nil)
      course_sharded_id = course_sharded_id_string

      # Build common data structure
      base_data = {
        id: item.canvas_id,
        item_type: item.canvas_content_type,
        requirement_status: req_status,
        requirement_type: progression.requirement_type,
        course_content_path: generate_content_path(item, progression),
        organization_shard_id: organization_shard_id,
        canvas_course_sharded_id: course_sharded_id
      }

      # Add assignment-specific or non-assignment data
      assignment_data = submission ? build_assignment_data(item, progression, submission, assignment) : build_non_assignment_data(progression)

      base_data.merge(assignment_data)
    end

    def build_assignment_data(item, progression, submission, assignment)
      calculated_score = calculate_score_percent(submission.score, submission.points_possible)

      {
        canvas_assignment_id: item.canvas_assignment_id,
        assignment_name: assignment&.title || progression.canvas_content_title,
        assignment_due_at: assignment&.due_at,
        canvas_user_id: submission.canvas_user_id,
        due_date: submission.due_at || progression.due_at,
        completed_date: submission.submitted_at,
        graded_at: submission.graded_at,
        points_possible: submission.points_possible,
        score: submission.score,
        score_percent: calculated_score,
        letter_grade: fetch_letter_grade(calculated_score),
        color_code: fetch_color_code(calculated_score),
        submission_status: submission.workflow_state
      }
    end

    def build_non_assignment_data(progression)
      {
        canvas_assignment_id: nil,
        assignment_name: progression.canvas_content_title,
        assignment_due_at: progression.due_at,
        canvas_user_id: @user.canvas_id,
        due_date: progression.due_at || progression.todo_date || progression.lock_at,
        completed_date: progression.requirement_status == 'completed' ? progression.updated_at : nil,
        graded_at: nil,
        points_possible: nil,
        score: nil,
        score_percent: nil,
        letter_grade: nil,
        color_code: nil,
        submission_status: nil
      }
    end

    def course_sharded_id_string
      organization_shard_id ? course_sharded_id(@course.canvas_id).to_s : @course.canvas_id.to_s
    end

    # Determines the requirement status based on progression and submission data
    def determine_requirement_status(progression, submission)
      # For assignment items, use submission status if available
      return submission.completed_status if submission && progression.requirement_status == 'completed'

      return submission.requirement_status if submission

      # For non-assignment items, map progression status to display status
      case progression.requirement_status
      when 'completed'
        'completed'
      when 'incomplete'
        if past_due?(progression)
          'past due'
        else
          'not completed'
        end
      else
        'not completed'
      end
    end

    # Generates the generic Canvas module item path for all content types with sharding support
    def generate_content_path(item, _progression)
      course_sharded_id = course_sharded_id_string
      "courses/#{course_sharded_id}/modules/items/#{item.canvas_id}"
    end

    def same_shard?
      request_org&.canvas_shard_id.to_s == organization_shard_id.to_s
    end

    # Checks if an item is past due based on progression dates
    def past_due?(progression)
      due_date = progression.todo_date || progression.due_at || progression.lock_at
      due_date && due_date < Time.current
    end

    private

    def fetch_active_modules
      @course.context_modules
             .includes(:context_module_items, :context_module_progressions)
             .where(workflow_state: 'active')
             .where(context_module_items: { canvas_id: @course.module_requiment_ids, workflow_state: 'active' })
             .where(context_module_progressions: { canvas_user_id: @user.canvas_id })
             .order('context_modules.position ASC, context_module_items.position ASC')
    end

    def preload_module_data(modules)
      module_item_ids = modules.flat_map { |mod| mod.context_module_items.map(&:canvas_id) }
      assignment_ids = modules.flat_map { |mod| mod.context_module_items.map(&:canvas_assignment_id).compact }

      {
        progressions: preload_progressions(module_item_ids),
        submissions: preload_submissions(assignment_ids),
        assignments: preload_assignments(assignment_ids)
      }
    end

    def preload_progressions(module_item_ids)
      ContextModuleProgression.where(
        canvas_module_item_id: module_item_ids,
        canvas_user_id: @user.canvas_id
      ).index_by(&:canvas_module_item_id)
    end

    def preload_submissions(assignment_ids)
      Submission.where(
        canvas_assignment_id: assignment_ids,
        canvas_user_id: @user.canvas_id
      ).index_by(&:canvas_assignment_id)
    end

    def preload_assignments(assignment_ids)
      Assignment.where(canvas_id: assignment_ids).index_by(&:canvas_id)
    end

    def build_module_data(modules, preloaded_data)
      data = []
      modules.each do |mod|
        module_record = process_module(mod, preloaded_data)
        data << module_record if module_record[:items_count].positive?
      end
      data
    end

    def process_module(mod, preloaded_data)
      rec = initialize_module_record(mod)

      mod.context_module_items.each do |item|
        process_module_item(item, rec, preloaded_data)
      end

      rec
    end

    def initialize_module_record(mod)
      cmp = mod.context_module_progressions
      module_progress_status = cmp.map(&:module_progress_status).uniq.first || nil
      {
        module_id: mod.canvas_id,
        name: mod.name,
        module_progress_status: module_progress_status,
        items_count: 0,
        items: []
      }
    end

    def process_module_item(item, rec, preloaded_data)
      progression = preloaded_data[:progressions][item.canvas_id]
      return unless progression

      submission, assignment = get_submission_and_assignment(item, preloaded_data)
      req_status = determine_requirement_status(progression, submission)

      return if filtered_out_item?(submission, req_status)

      item_data = {
        item: item,
        progression: progression,
        submission: submission,
        assignment: assignment
      }
      update_module_record(rec, req_status, item_data)
    end

    def get_submission_and_assignment(item, preloaded_data)
      return [nil, nil] unless item.canvas_assignment_id.present?

      submission = preloaded_data[:submissions][item.canvas_assignment_id]
      assignment = preloaded_data[:assignments][item.canvas_assignment_id]
      [submission, assignment]
    end

    def update_module_record(rec, req_status, item_data)
      rec[req_status] = rec[req_status].to_i + 1
      rec[:items_count] += 1
      rec[:items] << build_item_json(
        item_data[:item],
        item_data[:progression],
        req_status,
        submission: item_data[:submission],
        assignment: item_data[:assignment]
      )
    end

    def sql_joins(canvas_course_ids)
      <<~SQL.squish
        INNER JOIN (#{user_courses_with_requirement_counts(canvas_course_ids)}) req_counts ON req_counts.canvas_course_id = courses.canvas_id
        LEFT OUTER JOIN #{Enrollment.quoted_table_name} e
          ON e.canvas_user_id = #{user_id}
          AND e.canvas_course_id = courses.canvas_id
          AND e.base_role_type = 'StudentEnrollment' AND (e.workflow_state <> 'deleted')
        LEFT OUTER JOIN #{Score.quoted_table_name} scores ON scores.canvas_enrollment_id = e.canvas_id
      SQL
    end

    def sql_selects
      <<~SQL.squish
        courses.canvas_id AS canvas_course_id,
        e.canvas_id AS canvas_enrollment_id,
        scores.current_score AS current_score,
        scores.current_letter_grade AS current_letter_grade,
        req_counts.requirement_count AS requirement_count,
        req_counts.requirement_completed_count AS requirement_completed_count,
        req_counts.past_due_requirements_count AS past_due_requirements_count,
        req_counts.requirements_with_due_date_count AS requirements_with_due_date_count
      SQL
    end

    def calculate_course_progress_percent(course_progress)
      return 0 unless course_progress&.requirement_count&.positive?

      ((course_progress.requirement_completed_count.to_f / course_progress.requirement_count) * 100).round(0)
    end

    # Expected Course Progress = number of course module requirements with past due date / number of total course module requirements with due_date in the course
    def calculate_expected_course_progress(course_progress)
      return 0 unless course_progress&.requirements_with_due_date_count&.positive?

      ((course_progress.past_due_requirements_count.to_f / course_progress.requirements_with_due_date_count) * 100).round(0)
    end

    # Percentage of Expected Course Progress = percentage of items completed vs Expected Course Progress
    def calculate_expected_course_progress_percent(course_progress)
      expected_progress = calculate_expected_course_progress(course_progress)
      return 0 if expected_progress.zero?

      (calculate_course_progress_percent(course_progress).to_f / expected_progress * 100).round(0)
    end

    def calculate_score_percent(score, points_possible)
      return 0.0 if points_possible.to_f <= 0

      ((score.to_f / points_possible) * 100).round(2)
    end

    def fetch_letter_grade(score)
      grading_scheme&.score_to_grade(score)&.dig('name')
    end

    def fetch_color_code(score)
      grading_scheme_color = grading_scheme_colors&.by_score(score)
      grading_scheme_color&.color_code || grading_scheme_color&.default_color_code
    end

    def grading_scheme
      @grading_scheme ||= @course&.grading_scheme
    end

    def grading_scheme_colors
      @grading_scheme_colors ||= grading_scheme&.grading_scheme_colors
    end

    def create_module_celebration_events(modules)
      modules.each do |mod|
        next unless mod[:module_progress_status] == 'completed'

        CelebrationEvent.find_or_create_by!(
          canvas_user_id: @user.canvas_id,
          canvas_course_id: @course.canvas_id,
          event_type: 'module_completed',
          canvas_module_id: mod[:module_id]
        )
      end
    rescue StandardError => e
      Rails.logger.error "Error creating module celebration events: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end
end
