# frozen_string_literal: true

module QueryBuilder
  class StudentDueAssignments < BaseQueryBuilder
    def self.across_shard(student, options = {})
      all_requirements = []
      student.against_shards do |shard_student|
        requirements = StudentDueAssignments.new({
                                                   canvas_user_id: shard_student.canvas_id,
                                                   organization: current_organization,
                                                   filters: options
                                                 }).records
        all_requirements += requirements
      end
      all_requirements
    end

    def records
      # Get all module items with their progressions (both completed and incomplete)
      result = Course.selectable.joins(sql_joins)
                     .select(sql_selects)
                     .where(sql_where_conditions)
                     .order(Arel.sql(sql_order_clause))

      # Add Canvas URLs and paths for all content types
      result.map do |row|
        canvas_course_id = if row.organization_shard_id != 1 && row.canvas_course_id < PandaPal::Organization::SHARD_OFFSET
                             (PandaPal::Organization::SHARD_OFFSET * row.organization_shard_id) + row.canvas_course_id
                           else
                             row.canvas_course_id
                           end

        # Generate appropriate URLs based on content type
        urls = generate_content_urls(row, canvas_course_id)

        row.attributes.merge(urls)
      end
    end

    def sql_selects
      <<~SQL.squish
        #{basic_selects},
        #{requirement_status_case_sql} as req_status,
        #{organization_selects}
      SQL
    end

    private

    def basic_selects
      <<~SQL.squish
        context_module_items.canvas_id AS id,
        context_module_items.canvas_content_type AS item_type,
        context_module_items.canvas_assignment_id,
        COALESCE(assignments.title, context_module_progressions.canvas_content_title, 'Untitled Item') AS assignment_title,
        COALESCE(assignments.due_at) AS assignment_due_at,
        COALESCE(submissions.due_at, assignments.due_at, context_module_progressions.todo_date) AS submission_due_at,
        context_module_progressions.todo_date,
        context_module_progressions.lock_at,
        courses.canvas_id AS canvas_course_id,
        courses.name AS course_name,
        courses.sis_id AS course_sis_id,
        courses.course_code AS course_code,
        courses.workflow_state AS course_state,
        context_module_progressions.canvas_user_id AS canvas_user_id,
        submissions.score AS submission_score,
        submissions.points_possible AS submission_points_possible,
        submissions.workflow_state AS submission_state,
        context_module_progressions.requirement_status,
        context_module_progressions.requirement_type,
        context_module_progressions.canvas_page_url,
        context_module_progressions.canvas_content_id
      SQL
    end

    def requirement_status_case_sql
      <<~SQL.squish
        ( CASE
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') AND COALESCE((submissions.score / NULLIF(submissions.points_possible,0))*100,0) > 80 THEN 'mastered'
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') AND COALESCE((submissions.score / NULLIF(submissions.points_possible,0))*100,0) <= 80 THEN 'not mastered'
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('graded', 'submitted') THEN 'not mastered'
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IN ('submitted') THEN 'submitted'
          WHEN context_module_items.canvas_assignment_id IS NOT NULL AND submissions.workflow_state IS NOT NULL THEN 'not started'
          WHEN context_module_items.canvas_assignment_id IS NULL AND context_module_progressions.requirement_status = 'completed' THEN 'mastered'
          WHEN context_module_items.canvas_assignment_id IS NULL AND context_module_progressions.requirement_status = 'incomplete' AND COALESCE(context_module_progressions.todo_date, context_module_progressions.due_at, context_module_progressions.lock_at) < NOW() THEN 'past due'
          WHEN context_module_items.canvas_assignment_id IS NULL AND context_module_progressions.requirement_status = 'incomplete' THEN 'not completed'
          WHEN context_module_items.canvas_assignment_id IS NULL AND context_module_progressions.requirement_status IS NULL THEN 'not started'
          ELSE 'not started'
          END )
      SQL
    end

    def organization_selects
      <<~SQL.squish
        '#{organization_id}' AS organization_id,
        '#{organization_name}' AS organization_name,
        '#{organization_base_url}' AS organization_base_url,
        #{organization_shard_id} AS organization_shard_id
      SQL
    end

    def sql_joins
      <<~SQL.squish
        INNER JOIN #{Enrollment.quoted_table_name} enrollments
          ON enrollments.canvas_user_id = #{user_id}
          AND enrollments.canvas_course_id = courses.canvas_id
          AND enrollments.base_role_type = 'StudentEnrollment' AND (enrollments.workflow_state <> 'deleted')
        INNER JOIN (#{context_modules_sql}) c_r ON c_r.canvas_context_id = courses.canvas_id AND c_r.canvas_context_type = 'Course' AND c_r.workflow_state = 'active'
        INNER JOIN context_module_items ON context_module_items.canvas_id = c_r.req_id AND context_module_items.workflow_state = 'active'
        LEFT JOIN context_module_progressions ON context_module_progressions.canvas_module_item_id = context_module_items.canvas_id AND context_module_progressions.canvas_user_id = #{user_id}
        LEFT JOIN assignments ON assignments.canvas_id = context_module_items.canvas_assignment_id
        LEFT JOIN submissions ON submissions.canvas_assignment_id = assignments.canvas_id AND submissions.canvas_user_id = #{user_id}
      SQL
    end

    def generate_content_urls(row, canvas_course_id)
      # Use generic module item path for all content types
      item_id = needs_sharding?(row, row.id) ? "#{row.organization_shard_id}~#{row.id}" : row.id
      path = "courses/#{canvas_course_id}/modules/items/#{item_id}"

      {
        'course_content_path' => path
      }
    end

    def needs_sharding?(row, id)
      row.organization_shard_id != 1 && id.to_i < PandaPal::Organization::SHARD_OFFSET
    end

    def sql_where_conditions
      conditions = []

      # Past due filter
      conditions << past_due_condition if filters[:only_past_dues].present?

      # Date filter
      conditions << date_filter_condition if filters[:due_date].present? && !filters[:weekly].present?

      # Weekly filter
      conditions << weekly_filter_condition if filters[:weekly].present? && filters[:due_date].present?

      conditions.any? ? conditions.join(' AND ') : '1=1'
    end

    def sql_order_clause
      <<~SQL.squish
        COALESCE(submissions.due_at, assignments.due_at, context_module_progressions.todo_date) ASC NULLS LAST
      SQL
    end

    def past_due_condition
      <<~SQL.squish
        (
          COALESCE(submissions.due_at, assignments.due_at, context_module_progressions.todo_date) < NOW() - INTERVAL '1 day'
          AND #{requirement_status_case_sql} NOT IN ('mastered', 'completed')
        )
      SQL
    end

    def date_filter_condition
      target_date = Time.zone.parse(filters[:due_date])
      start_of_day = target_date.beginning_of_day.utc
      end_of_day = target_date.end_of_day.utc

      <<~SQL.squish
        COALESCE(submissions.due_at, assignments.due_at, context_module_progressions.todo_date)
        BETWEEN '#{start_of_day}' AND '#{end_of_day}'
      SQL
    end

    def weekly_filter_condition
      date = Time.zone.parse(filters[:due_date])
      start_date = date.beginning_of_week.utc
      end_date = (date.beginning_of_week + 4.days).end_of_day.utc

      <<~SQL.squish
        COALESCE(submissions.due_at, assignments.due_at, context_module_progressions.todo_date)
        BETWEEN '#{start_date}' AND '#{end_date}'
      SQL
    end
  end
end
