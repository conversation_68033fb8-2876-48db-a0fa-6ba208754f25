import { useEffect, useState } from 'react';
import { Overlay } from '@instructure/ui-overlays';
import { View } from '@instructure/ui-view';

const CelebrationOverlay = ({
  open,
  onClose,
  gifSrc,
  videoSrc,
  audioSrc,
  playAudio = false,
  duration = 5000,
  altText = "Fireworks Animation"
}) => {
  const [show, setShow] = useState(open);

  useEffect(() => {
    if (open) {
      setShow(true);
      if (playAudio && audioSrc) {
        const audio = new Audio(audioSrc);
        audio.play().catch(() => {});
      }
      const timer = setTimeout(() => {
        setShow(false);
        if (onClose) onClose();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [open, playAudio, audioSrc, duration, onClose]);

  if (!show) return null;

  const renderVideo = () => {
    return (
      <View as="div" className="fireworks-video-overlay">
        <video
          src={videoSrc}
          autoPlay
          loop
          muted
          playsInline
          className="fireworks-video"
          style={{ width: '100%', height: '100%' }}
          aria-label={altText}
        />
      </View>
    );
  }

  const renderGif = () => {
    return (
      <View as="div" className="fireworks-gif-overlay">
        <img
          src={gifSrc}
          alt={altText}
          style={{ width: '100%', height: '100%' }}
          className="fireworks-gif"
        />
      </View>
    );
  }

  return (
    <Overlay
      open={show}
      onDismiss={() => {
        setShow(false);
        if (onClose) onClose();
      }}
      shouldCloseOnEscape={false}
      defaultFocusElement={() => document.body}
      label='Celebration Overlay'
    >
      {videoSrc ? renderVideo() : renderGif()}
    </Overlay>
  );
};

export default CelebrationOverlay;
