// Theme asset imports
// Forest
import forestCourseBackground from '../assets/themes/forest/images/background/courses_background.jpg';
import forestAnnouncementBackground from '../assets/themes/forest/images/background/messages_background.jpg';
import forestResourceBackground from '../assets/themes/forest/images/background/resources_background.jpg';

import forestCloud from '../assets/themes/forest/images/elements/cloud.png';
import forestCloud2 from '../assets/themes/forest/images/elements/cloud2.png';
import forestCloud3 from '../assets/themes/forest/images/elements/cloud3.png';
import forestSun from '../assets/themes/forest/images/elements/sun.png';
import forestFoxWaveElement from '../assets/themes/forest/images/elements/fox_wave-marge.png'
import ForestPastdueForWave from '../assets/themes/forest/images/elements/past-due-fox-wave.png'
import ForestPastdueHelpfulFor from '../assets/themes/forest/images/elements/past-due-helpful-fox.png'

// Winter
import winterCourseBackground from '../assets/themes/winter/images/background/courses_background.jpg';
import winterAnnouncementBackground from '../assets/themes/winter/images/background/messages_background.jpg';
import winterResourceBackground from '../assets/themes/winter/images/background/resources_background.jpg';

import winterFoxOverdue from '../assets/themes/winter/images/mascot/fox_overdue.png'
import winterFoxWave from '../assets/themes/winter/images/mascot/fox_wave.png'

import completionArtForWinter from '../assets/themes/winter/images/mascot/treasure.png'


// Space
import spaceCourseBackground from '../assets/themes/space/images/background/courses_background.jpg';
import spaceAnnouncementBackground from '../assets/themes/space/images/background/messages_background.jpg';
import spaceResourceBackground from '../assets/themes/space/images/background/resources_background.jpg';

import spaceFoxOverdue from '../assets/themes/space/images/mascot/fox_overdue.png'
import spaceFoxWave from '../assets/themes/space/images/mascot/fox_wave.png'

import completionArtForSpace from '../assets/themes/space/images/mascot/treasure.png'


// Celebration audio & video
import celebrationAudio from '../assets/themes/shared/audio/celebration.mp3';
import gif from  "../assets/themes/shared/audio/firework.gif"
import celebrationSpaceVideo from  "../assets/themes/shared/video/space_trophy.mp4"
import celebrationForestVideo from  "../assets/themes/shared/video/forest_trophy.mp4"

export const getCourseThemeBackground = (theme) => {
  switch (theme) {
    case 'forest':
      return forestCourseBackground;
    case 'winter':
      return winterCourseBackground;
    case 'space':
      return spaceCourseBackground;
    default:
      return null;
  }
};

export const getAnnouncementThemeBackground = (theme) => {
  switch (theme) {
    case 'forest':
      return forestAnnouncementBackground;
    case 'winter':
      return winterAnnouncementBackground;
    case 'space':
      return spaceAnnouncementBackground;
    default:
      return null;
  }
};

export const getResourceThemeBackground = (theme) => {
  switch (theme) {
    case 'forest':
      return forestResourceBackground;
    case 'winter':
      return winterResourceBackground;
    case 'space':
      return spaceResourceBackground;
    default:
      return null;
  }
};

export const getThemeElements = (theme) => {
  switch (theme) {
    case 'forest':
      return {
        foxWave: forestFoxWaveElement,
        foxOverdue: ForestPastdueHelpfulFor,
        PastdueFoxWave: ForestPastdueForWave,
        completionArt: completionArtForWinter,
        celebrationAudio: celebrationAudio,
        gif: gif,
        celebrationVideo: celebrationForestVideo,
      };
    case 'winter':
      return {
        foxOverdue: winterFoxOverdue,
        foxWave: winterFoxWave,
        completionArt: completionArtForWinter,
        celebrationAudio: celebrationAudio,
        gif: gif,
        celebrationVideo: celebrationSpaceVideo,
      };
    case 'space':
      return {
        foxOverdue: spaceFoxOverdue,
        foxWave: spaceFoxWave,
        completionArt: completionArtForSpace,
        celebrationAudio: celebrationAudio,
        gif: gif,
        celebrationVideo: celebrationSpaceVideo,
      };
    default:
      return {
        celebrationAudio: celebrationAudio,
        celebrationVideo: celebrationSpaceVideo
      };
  }
};

export const getThemeColors = (theme) => {
  switch (theme) {
    case 'forest':
      return {
        primary: '#2D5016',
        secondary: '#4A7C59',
        accent: '#8FBC8F',
        background: '#d9f1fd',
      };
    case 'winter':
      return {
        primary: '#1E3A8A',
        secondary: '#3B82F6',
        accent: '#93C5FD',
        background: '#76bde9',
      };
    case 'space':
      return {
        primary: '#4C1D95',
        secondary: '#7C3AED',
        accent: '#C4B5FD',
        background: '#0e1d30',
      };
      case 'no_theme':
        return {
          primary: '#4C1D95',
          secondary: '#7C3AED',
          accent: '#C4B5FD',
          background: '#F5F5F5',
        };
    default:
      return {
        primary: '#374151',
        secondary: '#6B7280',
        accent: '#9CA3AF',
        background: '#F5F5F5',
      };
  }
};
