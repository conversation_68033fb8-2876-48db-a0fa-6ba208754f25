import { useEffect, useState} from 'react';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Flex } from '@instructure/ui-flex';
import { Link } from '@instructure/ui-link';
import { Alert } from '@instructure/ui-alerts';
import { Heading } from '@instructure/ui-heading';
import { Spinner } from '@instructure/ui-spinner';
import {
  IconInfoLine,
  IconWarningLine,
  IconQuestionLine,
  IconCalendarMonthLine
} from '@instructure/ui-icons';
import { InfoAlert } from '../../shared/components/UtilUI';

import * as API from "../../utils/api";

const AnnouncementsTab = (props) => {
  const { studentId, announcements = [], onAnnouncementsChange } = props;
  const [isLoading, setIsLoading] = useState(false);

  const handleDismissAnnouncement = (announcementId) => {
    setIsLoading(true);
    API.dismissAnnouncement(studentId, announcementId)
      .then(() => {
        // Remove the dismissed announcement from the local state
        const updatedAnnouncements = announcements.filter(announcement => announcement.id !== announcementId);
        onAnnouncementsChange(updatedAnnouncements);
        setIsLoading(false);
      })
      .catch(error => {
        console.error('Failed to dismiss announcement', error);
        setIsLoading(false);
      });
  };

  const getAnnouncementIcon = (iconType) => {
    switch (iconType) {
      case 'information':
        return <IconInfoLine />;
      case 'warning':
        return <IconWarningLine />;
      case 'error':
        return <IconWarningLine />;
      case 'question':
        return <IconQuestionLine />;
      case 'calendar':
        return <IconCalendarMonthLine />;
      default:
        return <IconInfoLine />;
    }
  };

  const getAnnouncementVariant = (iconType) => {
    switch (iconType) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'information':
      case 'question':
      case 'calendar':
      default:
        return 'info';
    }
  };

  const renderNotificationSettings = () => {
    const canvasUrl = window.location.ancestorOrigins?.[0] || window.ENV?.canvas_url;
    const notificationPreferencesUrl = canvasUrl ? `${canvasUrl}/profile/communication` : '#';
    const globalAnnouncementsUrl = canvasUrl ? `${canvasUrl}/account_notifications` : '#';

    return (
      <View
        as="div"
        background="secondary"
        padding="small"
        borderRadius="medium"
        borderWidth="small"
        width="40%"
        margin='none none none xx-large'
        themeOverride={{
          backgroundSecondary: '#ffffff',
        }}
      >

        <Heading level="h4" margin="0 0 small 0">Notification Settings</Heading>
        <Text as="div" margin="0 0 small 0" size="small">
          Tell us how and when you would like to be notified of events in Canvas.
        </Text>
        <View as="div" margin="0 0 small 0" size="medium">
          <Link href={notificationPreferencesUrl} target="_parent" isWithinText={true} size='small'>
            Set Canvas Notification Preferences
          </Link>
        </View>
        <View as="div">
          <Link href={globalAnnouncementsUrl} target="_parent" isWithinText={false} size='small'>
            View All Global Announcements in Canvas
          </Link>
        </View>
      </View>
    );
  };

  const renderAnnouncements = () => {
    if (announcements.length === 0) {
      return (
        <InfoAlert
          subText="You have no un-dismissed Global Announcements at this time. Course announcements are accessible within your Canvas courses."
        />
      );
    }

    return announcements.map((announcement, index) => (
      <View
        key={`announcement-wrapper-${index}`}
        margin="0 0 small 0"
        maxWidth="880px"
        display="block"
      >
        <Alert
          variant={getAnnouncementVariant(announcement.icon)}
          renderCloseButtonLabel="Dismiss announcement"
          renderCustomIcon={() => getAnnouncementIcon(announcement.icon)}
          onDismiss={() => handleDismissAnnouncement(announcement.id)}
        >
          <Text size="medium" weight="bold">{announcement.subject}</Text>
          <View
            as="div"
            textAlign="start"
            lineHeight="condensed"
            dangerouslySetInnerHTML={{ __html: announcement.message }}
          />
        </Alert>
      </View>
    ));
  };

  const renderSpinner = () =>{
    return (
      <Flex justifyItems="center" alignItems="center" height="100%">
        <Spinner renderTitle="Loading announcements..." size="medium" />
      </Flex>
    );
  }

  return (
    <View as="div" padding="medium" textAlign="start" className='header-text' overflowX="hidden !important" height="100%">
      <Text size="large" weight="bold" >Global Announcements </Text>({announcements.length})
      <Flex direction="row" alignItems="start" gap="medium">
        <Flex.Item size="50%" shouldShrink>
          <View as="div" minWidth="0">
            {
            isLoading ? renderSpinner() : renderAnnouncements()
            }
          </View>
        </Flex.Item>
        <Flex.Item size="50%" shouldShrink margin="none none none xx-large">
          {renderNotificationSettings()}
        </Flex.Item>
      </Flex>
    </View>
  );
}

export default AnnouncementsTab;
