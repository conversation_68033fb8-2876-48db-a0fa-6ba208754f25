import { useEffect, useState } from 'react';
import { View } from '@instructure/ui-view';
import { Text } from '@instructure/ui-text';
import { Avatar } from '@instructure/ui-avatar';
import { Flex } from '@instructure/ui-flex';
import { Link } from '@instructure/ui-link';

import * as API from "../../utils/api";

const TeachersTab = (props) => {
  const { studentId } = props;
  const [teachers, setTeachers] = useState([]);

  useEffect(() => {
    getTeachersContactList();
  }, []);

  const getTeachersContactList = () => {
    API.getTeacherContacts(studentId)
      .then((response) => response.data)
      .then((response) => {
        setTeachers(response.teacher_contacts);
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const randomAvatarGradientColor = (index) => {
    const bottomColors = ["#FC5E13", "#E317DD", "#096B19", "#7E00FE", "#034AF4", "#EE1502", "#008EE2"];
    // Lighter versions of bottomColors for topColors
    const topColors = ["#FFD580", "#EAB3F0", "#B3E6B3", "#D2BBE4", "#B3D6F0", "#F2B3B3", "#C1DFE4"];
    return [topColors[index%7], bottomColors[index%7]];
  }

  const renderEmail = (emails) => {
    if (!emails || emails.length === 0) {
      return null;
    }

    return (
      <>
        <View as="div">
          <Text size="small" weight="weightImportant">Email:</Text>
        </View>
        {emails.map((email, idx) => (
          <View as="div" key={idx}>
            <Text size="small" wrap="break-word">
              <Link href={`mailto:${email}`}>{email}</Link>
            </Text>
          </View>
        ))}
      </>
    );
  }

  const renderPhoneNumber = (phoneNumbers) => {
    if (!phoneNumbers || phoneNumbers.length === 0) {
      return null;
    }

    return (
      <>
        <View as="div" padding="small none none none">
          <Text size="small" weight="weightImportant">Phone:</Text>
        </View>
        {phoneNumbers.map((phone, idx) => (
          <View as="div" key={idx}>
            <Text size="small" wrap="break-word">{phone}</Text>
          </View>
        ))}
      </>
    );
  }

  const renderCard = (teacher, index) => {
    const [topGradientColor, bottomGradientColor] = randomAvatarGradientColor(index);
    return (
      <View
        as="div"
        margin="medium"
        textAlign="center"
        shadow="above"
        background="primary"
        borderRadius="large"
        maxWidth="55%"
        minHeight="6rem"
        key={`teacherCard-${teacher.id}`}
      >
        <Flex padding="medium small" alignItems="center">
          <Flex.Item
            padding="x-small"
            textAlign="start"
            size="8%"
          >
            <Avatar
              name={teacher.name}
              size="small"
              color="ai"
              hasInverseColor
              margin="0 space8 0 0"
              themeOverride={{
                aiTopGradientColor: `${topGradientColor}`,
                aiBottomGradientColor: `${bottomGradientColor}`,
                fontWeight: '400'
               }}
            />
          </Flex.Item>
          <Flex.Item
            padding="x-small"
            textAlign="start"
            size="46%"
          >
            <Text size="descriptionPage" wrap="break-word">
              {teacher.name}
            </Text>
          </Flex.Item>
          <Flex.Item
            padding="x-small"
            textAlign="end"
            size="46%"
          >
          { renderEmail(teacher.email) }
          { renderPhoneNumber(teacher.phone_numbers) }
          </Flex.Item>
        </Flex>
      </View>
    )
  }

  const renderTeachersContacts = () => {
    return teachers.map((teacher, index) => {
      return renderCard(teacher, index)
    })
  }

  return (
 
    <View as="div">
      <View as="div" textAlign="start" margin="small medium" className='header-text'>
        <Text size="large" weight="bold" >My Teachers </Text> ({teachers.length})
      </View>
      <View as="div" textAlign="start">
      { renderTeachersContacts() }
      </View>
    </View>
  )
}

export default TeachersTab
