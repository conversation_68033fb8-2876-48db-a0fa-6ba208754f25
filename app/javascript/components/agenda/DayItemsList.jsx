import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { Link } from '@instructure/ui-link';
import { But<PERSON> } from '@instructure/ui-buttons';
import { IconAssignmentLine,
         IconQuizLine,
         IconArrowEndLine,
         IconPublishLine,
         IconWarningBorderlessLine,
         IconXLine,
         IconCheckLine,
         IconStarLine,
         IconStarLightLine,
         IconClockLine,
         IconCalendarMonthLine,
         IconDocumentLine,
         IconDiscussionLine,
         IconLtiLine,
         IconFolderLine } from '@instructure/ui-icons';

import { DateTime } from 'luxon';
import InstTable from '../../shared/components/InstTable';
import { IconCircle } from '../../shared/components/UtilUI';
import * as Util from "../../utils/helpers";
import * as API from "../../utils/api";

const DayItemsList = (props) => {
  const { studentId, combinedItems, setRefetchFlag } = props;

  const getItemIcon = (itemType) => {
    switch (itemType) {
      case 'Assignment':
        return <IconAssignmentLine />;
      case 'Quizzes::Quiz':
      case 'Quiz':
        return <IconQuizLine />;
      case 'WikiPage':
      case 'Page':
        return <IconDocumentLine />;
      case 'DiscussionTopic':
      case 'Discussion':
        return <IconDiscussionLine />;
      case 'ExternalTool':
        return <IconLtiLine />;
      case 'File':
        return <IconFolderLine />;
      case 'calendar_event':
        return <IconCalendarMonthLine />;
      default:
        return <IconAssignmentLine />;
    }
  };

  const renderTitle = (row, layout) => {
    const isEvent = row.item_type === 'calendar_event';
    const itemIcon = getItemIcon(row.item_type);

    // Use course_content_path for all module items
    let item_canvas_url = Util.getItemCanvasUrl(row['course_content_path'], studentId);

    const linkUrl = isEvent
      ? Util.isSameUser(window.ENV.user.canvas_id, row.canvas_user_id)
        ? Util.canvasCalendarEventUrl(row.html_path)
        : ''
      : item_canvas_url;

    return (
      <View as="div">
        <Flex direction="column">
          <Flex.Item>
            <Text size="x-small">
              <b>{isEvent ? 'Event:' : 'Course:'}</b>&nbsp;
              {isEvent
                ? DateTime.fromISO(row['start_at'], { setZone: true })
                          .toFormat('h:mm a') // Force 12-hour + am/pm
                          .replace(/(am|pm)/i, (match) => match.toUpperCase())
                : row['course_name']}
            </Text>
          </Flex.Item>
          <Flex.Item>
            <Text color="brand">
              <Link
                isWithinText={false}
                href={linkUrl}
                onClick={
                  row.item_type !== 'calendar_event'
                    ? Util.handleCanvasAssignmentUrlNavigation(item_canvas_url, row['canvas_course_id'], row['canvas_assignment_id'] || row['id'], studentId)
                    : undefined
                }
                target="_parent"
                renderIcon={itemIcon}
              >
                {isEvent ? row['title'] : row['assignment_title']}
              </Link>
            </Text>
          </Flex.Item>
        </Flex>
      </View>
    )
  }

  const markEventComplete = async (canvasUserId, eventId, organizationName) => {
    await API.markEventComplete(canvasUserId, eventId, { organization_name: organizationName })
      .then(() => {
        setRefetchFlag(true)
      })
      .catch((error) => {
        console.log(error);
      });
  }

  const renderReqStatus = (row, layout) => {
    if (row.item_type === 'calendar_event')
      return

    let icon = <IconCircle renderIcon={<IconStarLightLine />}
                background="secondary"
                height="1.6rem"
                width="1.6rem"
               />
    let text = 'Not Started';

    switch (row['req_status']) {
      case 'not mastered':
        icon = <IconCircle renderIcon={<IconXLine />}
                background="danger"
                height="1.6rem"
                width="1.6rem"
               />
        text = 'Not Mastered';
        break;
      case 'past due':
        icon = <IconCircle renderIcon={<IconWarningBorderlessLine />}
                borderColor="danger"
                height="1.6rem"
                width="1.6rem"
                themeOverride={{
                  color: '#E0061F'
                }}/>
        text = 'Past Due';
        break;
      case 'mastered':
        icon = <IconCircle renderIcon={<IconStarLine />}
                borderColor="success"
                height="1.6rem"
                width="1.6rem"
                themeOverride={{
                  color: '#0B874B',
                  backgroundPrimary: '#ffeb3b'
                }}/>
        text = 'Mastered';
        break;
      case 'submitted':
        icon = <IconCircle renderIcon={<IconCheckLine />}
                borderColor="info"
                height="1.6rem"
                width="1.6rem"
                themeOverride={{
                  color: '#0374B5'
                }}/>
        text = 'Submitted';
        break;
      case 'not completed':
        icon = <IconCircle renderIcon={<IconWarningBorderlessLine />}
                borderColor="warning"
                height="1.6rem"
                width="1.6rem"
                themeOverride={{
                  color: '#FC5E13'
                }}/>
        text = 'Not Completed';
        break;
      case 'not started':
      default:
        icon = <IconCircle renderIcon={<IconClockLine />}
                borderColor="secondary"
                height="1.6rem"
                width="1.6rem"
                themeOverride={{
                  color: '#8B969E'
                }}/>
        text = 'Not Started';
        break;
    }

    return (
      <View key={'ico_'+row['id']}><Text size="small">{icon} {text}</Text></View>
    )
  }

  const renderCompletionStatus = (row, layout) => {
    if (row.item_type === 'calendar_event') {
      if (Util.dateIsFuture(row.start_at)) return

      let returnVal = <IconPublishLine color="success"/>

      if (!row.completed_at){
        returnVal = Util.isSameUser(window.ENV.user.canvas_id, row.canvas_user_id) ? <Button color="success" size="small" onClick={() => markEventComplete(row.canvas_user_id, row.canvas_id, row.organization_name)}>Mark Complete</Button> : ''
      }

      return (
        <View>{returnVal}</View>
      )
    }
    // Use course_content_path for all module items
    let item_canvas_url = Util.getItemCanvasUrl(row['course_content_path'], studentId);

    let returnVal = <Link
                     isWithinText={false}
                     href={item_canvas_url}
                     onClick={Util.handleCanvasAssignmentUrlNavigation(item_canvas_url, row['canvas_course_id'], row['canvas_assignment_id'] || row['id'], studentId)}
                     target="_parent"
                     renderIcon={<IconArrowEndLine />}
                     iconPlacement="end"
                    >
                    Go
                    </Link>

    if (row['req_status'] == 'mastered'){
      returnVal = <IconPublishLine color="success"/>
    }
    return (
      <View>{returnVal}</View>
    )
  }

  const renderItemHeader = () => {
    let text = 'Items'
    if (combinedItems) {
      text = `Items (${combinedItems.length})`
    }

    return (
      <Text size="small">{text}</Text>
    )
  }

  const tableHeaders = [
    {
      id: 'items',
      text: renderItemHeader(),
      width: '55%',
      renderCell: renderTitle
    },
    {
      id: 'reqStatus',
      text: <Text size="small">Requirement Status</Text>,
      renderCell: renderReqStatus
    },
    {
      id: 'completionStatus',
      text: '',
      textAlign: 'end',
      renderCell: renderCompletionStatus
    }
  ]

  const renderAssignments = () => {
    return (
      <InstTable
        headers={tableHeaders}
        rows={combinedItems}
      />
    )
  }

  return (
    <View as="div">
      { renderAssignments() }
    </View>
  )
}

export default DayItemsList
