import { useEffect, useState } from 'react';

import { View } from '@instructure/ui-view';
import { Flex } from '@instructure/ui-flex';
import { Text } from '@instructure/ui-text';
import { Pill } from '@instructure/ui-pill';
import { Link } from '@instructure/ui-link';
import { IconAssignmentLine,
         IconQuizLine,
         IconWarningBorderlessLine,
         IconXLine,
         IconCheckLine,
         IconStarLine,
         IconStarLightLine,
         IconDocumentLine,
         IconDiscussionLine,
         IconExternalLinkLine,
         IconPaperclipLine,
         IconTextLine,
         IconModuleLine } from '@instructure/ui-icons';

import * as API from "../../utils/api";
import InstTable from '../../shared/components/InstTable';
import { CounterCircle, IconCircle } from '../../shared/components/UtilUI';
import * as Util from "../../utils/helpers";

const CourseModuleItems = (props) => {
  const { courseModule, course, studentId, totalItems } = props;
  const [ moduleItems, setModuleItems ] = useState([]);

  useEffect(() => {
    setModuleItems(courseModule['items']);
  }, [courseModule]);

  const getItemIcon = (itemType) => {
    switch (itemType) {
      case 'Assignment':
        return <IconAssignmentLine />;
      case 'Quizzes::Quiz':
      case 'Quiz':
        return <IconQuizLine />;
      case 'WikiPage':
      case 'Page':
        return <IconDocumentLine />;
      case 'DiscussionTopic':
      case 'Discussion':
        return <IconDiscussionLine />;
      case 'ExternalTool':
        return <IconExternalLinkLine />;
      case 'File':
        return <IconPaperclipLine />;
      default:
        return <IconModuleLine />;
    }
  };

  const renderTitle = (row, layout) => {
    const itemIcon = getItemIcon(row['item_type']);

    // Use course_content_path for all items with helper function
    const item_canvas_url = Util.getItemCanvasUrl(row['course_content_path'], studentId);

    // For assignments, use the existing navigation handler, for others use simple navigation
    const handleClick = row['canvas_assignment_id'] ?
      Util.handleCanvasAssignmentUrlNavigation(item_canvas_url, row['canvas_course_sharded_id'], row['canvas_assignment_id'], studentId) :
      null;

    return (
      <View as="div">
        <Text color="brand">
          <Link
           isWithinText={false}
           href={item_canvas_url}
           onClick={handleClick}
           target="_parent"
           renderIcon={itemIcon}
          >
            {row['assignment_name']}
          </Link>
        </Text>
        <Text size="x-small">
          <View as="div" padding="none medium">
            <strong>Type: </strong>{row['item_type']}
          </View>
        </Text>
      </View>
    )
  }

  const renderDueDate = (row, layout) => {
    return Util.formatDateString(row['due_date'])
  }

  const renderCompleteDate = (row, layout) => {
    return Util.formatDateString(row['completed_date'])
  }

  const renderReqStatus = (row, layout) => {
    let icon = <IconCircle renderIcon={<IconStarLightLine />}
                background="secondary"
               />
    let text = 'Not Started';

    switch (row['requirement_status']) {
      case 'not mastered':
        icon = <IconCircle renderIcon={<IconXLine />}
                background="danger"
               />
        text = 'Not Mastered';
        break;
      case 'past due':
        icon = <IconCircle renderIcon={<IconWarningBorderlessLine />}
                borderColor="danger"
                themeOverride={{
                  color: '#E0061F'
                }}/>
        text = 'Past Due';
        break;
      case 'mastered':
        icon = <IconCircle renderIcon={<IconStarLine />}
                borderColor="success"
                themeOverride={{
                  color: '#0B874B',
                  backgroundPrimary: '#ffeb3b'
                }}/>
        text = 'Mastered';
        break;
      case 'completed':
        icon = <IconCircle renderIcon={<IconCheckLine />}
                borderColor="success"
                themeOverride={{
                  color: '#0B874B'
                }}/>
        text = 'Completed';
        break;
    }

    return (
      <View key={'ico_'+(row['canvas_assignment_id'] || row['id'])}>{icon} {text}</View>
    )
  }

  const renderScoreGrade = (row, layout) => {
    // For non-assignment items, show N/A
    if (!row['canvas_assignment_id'] || row['points_possible'] == null) {
      return (
        <View as="div">
          <Text size="small" color="secondary">N/A</Text>
        </View>
      )
    }

    const colorCode = row['color_code'] ? row['color_code'] : '#c7cdd1';

    if (row['score'] == null) {
      return (
        <View as="div">
          <View display="inline-block">
            {row['score']}/{row['points_possible']} pts
          </View>
        </View>
      )
    } else {
      return (
        <View as="div">
          <View display="inline-block" margin="none xx-small">
            {row['score']}/{row['points_possible']}
          </View>
          <View
            background="secondary"
            display="inline-block"
            borderRadius="medium none none medium"
            padding="none x-small"
            shadow="resting"
            margin="none"
            {...props}
            themeOverride={{
              backgroundSecondary: colorCode+'1A',
              color: colorCode
            }}
            >
            {(Number(row['score_percent'])).toFixed(2)}%
          </View>
          <View
            background="primary-inverse"
            display="inline-block"
            borderRadius="none medium medium none"
            padding="none x-small"
            shadow="resting"
            margin="none"
            {...props}
            themeOverride={{
              backgroundPrimaryInverse: colorCode
            }}
            >
            {row['letter_grade']}
          </View>
        </View>
      )
    }
  }

  const tableHeaders = [
    {
      id: 'title',
      text: `Items (${totalItems})`,
      renderCell: renderTitle
    },
    {
      id: 'dueDate',
      text: 'Date Due',
      renderCell: renderDueDate,
      textAlign: 'center'
    },
    {
      id: 'completedDate',
      text: 'Date Completed',
      renderCell: renderCompleteDate,
      textAlign: 'center'
    },
    {
      id: 'score',
      text: 'Score | Grade',
      renderCell: renderScoreGrade,
      textAlign: 'center'
    },
    {
      id: 'reqStatus',
      text: 'Requirement Status',
      renderCell: renderReqStatus
    }
  ]

  const renderModuleItems = () => {
    return (
      <InstTable
        headers={tableHeaders}
        rows={moduleItems}
      />
    )
  }

  return(
    <View as="div">
      { renderModuleItems() }
    </View>
  )
}

export default CourseModuleItems
