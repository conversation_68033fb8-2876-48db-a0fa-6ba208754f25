# frozen_string_literal: true

class Api::V1::StudentsController < ApplicationController
  skip_authorization_check
  before_action :set_user, except: [:mark_event_complete]

  # show all student courses list
  def courses
    @records = QueryBuilder::StudentCourses.across_shard(@user)
    @default_course_image = GlobalSetting.where(setting_type: 'default_course_image').last
  end

  def course_progress
    @course_progress = QueryBuilder::StudentCourseProgress.from_valid_shard(@user, { canvas_course_id: params[:course_id], course_shard_id: params[:org_shard_id], filters: params[:filters] })

    render json: @course_progress
  rescue StandardError => e
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  def course_date_ranges
    @record = @user.course_date_ranges[0]
  rescue StandardError => e
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  def due_assignments
    due_date = params[:due_date]
    filters = {
      due_date: due_date,
      only_past_dues: params[:only_past_dues]
    }.compact_blank

    @assignments = QueryBuilder::StudentDueAssignments.across_shard(@user, filters)
    @calendar_events = calendar_events(due_date)
    @show_fireworks = false

    return unless due_date.present?

    assignments_present = @assignments.present?
    events = @calendar_events.values.flatten
    events_present = events.any?

    all_assignments_completed = !assignments_present || @assignments.all? { |a| a['requirement_status'] == 'completed' }
    all_events_completed = !events_present || events.all? { |e| e[:completed_at].present? }

    return unless all_assignments_completed && all_events_completed && (assignments_present || events_present)

    # Check or create daily celebration events
    Students::CelebrationEvents.create_daily_event(@user, due_date)

    # Check if there is an existing celebration event for this user and date
    shown_anywhere = Students::CelebrationEvents.daily_event_shown?(@user, due_date)
    @show_fireworks = !shown_anywhere
  end

  def weekly_due_assignments
    # Fetch all module requirements (assignments and non-assignments) from all shards for the student
    assignments = QueryBuilder::StudentDueAssignments.across_shard(@user, { due_date: params[:due_date], weekly: true })
    # Set week date range (Monday to Friday)
    date = Time.zone.parse(params[:due_date])
    @start_date = date.beginning_of_week
    @end_date = @start_date + 4.days

    @records = assignments.group_by do |item|
      due_at = item['submission_due_at'] || item['assignment_due_at'] || item['todo_date']
      Time.zone.parse(due_at.to_s).strftime('%Y-%m-%d') if due_at
    end
    @calendar_events = calendar_events(params[:due_date], weekly: true)
  end

  def calendar_events(due_date = nil, weekly: false)
    Students::CalendarEvents.fetch_events_for_user(@user, due_date, weekly: weekly)
  end

  def update_student_celebration_event
    raise ArgumentError, 'Due date or course ID must be provided' unless params[:due_date].present? || params[:course_id].present?

    if params[:due_date].present?
      Students::CelebrationEvents.mark_daily_event_shown(@user, params[:due_date])
    elsif params[:course_id].present?
      Students::CelebrationEvents.mark_module_event_shown(@user, params[:course_id])
    end
    head :ok
  rescue StandardError => e
    Rails.logger.error "Error updating celebration event: #{e.message}"
    render json: { error_message: e.message }, status: :unprocessable_entity
  end

  def mark_event_complete
    user = User.find_by!(canvas_id: params[:id])
    Students::CalendarEvents.mark_event_complete(
      user,
      params[:event_id],
      params[:organization_name],
      method(:with_organization),
      method(:current_organization)
    )
    head :ok
  end

  private

  def set_user
    @user = User.find_by(canvas_id: params[:id])
  end
end
