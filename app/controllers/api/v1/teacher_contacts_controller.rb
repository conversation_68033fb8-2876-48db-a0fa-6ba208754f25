# frozen_string_literal: true

class Api::V1::TeacherContactsController < ApplicationController
  skip_authorization_check
  before_action :set_user

  def index
    # Implement logic to fetch teacher contacts for the user from all his active courses across shards
    teacher_contacts = []

    @user.against_shards do |shard_user|
      next if shard_user.nil?

      # Get all active enrollments for the user in this shard
      student_enrollments = Enrollment.students.not_deleted
                                      .group('enrollments.canvas_course_id')
                                      .select('canvas_course_id')
                                      .where(canvas_user_id: shard_user.canvas_id)
      course_ids = student_enrollments.map(&:canvas_course_id)
      next if course_ids.empty?

      # Fetch all enrollments for these course IDs
      teacher_enrollments = Enrollment.not_deleted
                                      .where(canvas_course_id: course_ids, base_role_type: %w[TeacherEnrollment])
                                      .group('enrollments.canvas_user_id')
                                      .select('canvas_user_id')
      teacher_user_ids = teacher_enrollments.map(&:canvas_user_id)
      teachers = User.where(canvas_id: teacher_user_ids)
      next if teachers.empty?

      # Collect teacher contact information
      teachers.each do |teacher|
        teacher_contacts << {
          id: teacher.id,
          canvas_id: teacher.canvas_id,
          name: teacher.sortable_name,
          email: [teacher.email],
          phone_numbers: []
        }
      end
    end

    teacher_contacts = teacher_contacts.uniq.sort_by { |tc| tc[:name].downcase }
    render json: { teacher_contacts: teacher_contacts }
  end

  private

  def set_user
    @user = User.find_by!(canvas_id: params[:user_id])
  rescue ActiveRecord::RecordNotFound
    render json: { error_message: 'User not found' }, status: :not_found
  end
end
