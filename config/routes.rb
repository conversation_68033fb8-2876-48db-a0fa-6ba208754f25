require 'sidekiq/web'
require 'sidekiq-scheduler/web'
Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  if Rails.env.production?
    Sidekiq::Web.use(Rack::Auth::Basic) { |username, password| username == ENV['SIDEKIQ_USERNAME'] && password == ENV['SIDEKIQ_PASSWORD'] }
  end
  mount Sidekiq::Web => '/sidekiq'

  mount PandaPal::Engine, at: '/lti'
  mount CanvasSync::Engine, at: '/canvas_sync'

  root to: 'panda_pal/lti#launch'

  scope '/organizations/:organization_id' do
    lti_nav course_navigation: 'lti#course_navigation'
    lti_nav global_navigation: 'lti#global_navigation'
    lti_nav account_navigation: 'lti#account_navigation'

    namespace :api, defaults: { format: :json } do
      namespace :v1 do
        get '/access/check', to: 'access#check'

        resources :users, only: [:show] do
          resources :user_config
          get :dashboard_students, on: :member
          resources :announcements, only: [:index, :destroy]
          resources :teacher_contacts, only: [:index]
        end
        resources :students, only: [] do
          get :courses, on: :member
          get 'courses/:course_id', to: 'students#course_progress', on: :member
          put 'events/:event_id/mark_event_complete', to: 'students#mark_event_complete', on: :member
          get :due_assignments, on: :member
          get :course_date_ranges, on: :member
          get :weekly_due_assignments, on: :member
          patch :update_student_celebration_event, on: :member
        end
        resources :external_resources
        resources :global_settings
        resources :accounts, only: [] do
          resources :grade_scheme_colors
        end
      end
    end
  end
end
