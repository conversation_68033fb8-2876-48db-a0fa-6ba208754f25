// See the shakacode/shakapacker README and docs directory for advice on customizing your webpackConfig.
const { generateWebpackConfig } = require('shakapacker')

const webpack = require("webpack")
const path = require("path")

const webpackConfig = generateWebpackConfig(
  {
    plugins: [
        new webpack.ProvidePlugin({
            React: 'react',
        }),

        new webpack.DefinePlugin({
            "process.env.ALWAYS_APPEND_UI_TESTABLE_LOCATORS": 'true',
            "process.env.OMIT_INSTUI_DEPRECATION_WARNINGS": 'true',
        }),

    ].filter(Boolean),
    resolve: {
        alias: {
          '@shared': path.resolve(__dirname, '../..', 'app/javascript/shared'),
          '@': path.resolve(__dirname, '../..', 'app/javascript'),
        }
      }
  }
)

webpackConfig.module.rules.push({
  test: /\.(mp3|wav|ogg|mp4|mov)$/i,
  type: 'asset/resource',
});

module.exports = webpackConfig