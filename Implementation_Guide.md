# LTI Student Observer - Implementation Guide

## Phase 1: Foundation Setup (18 hours)

### 1.1 LTI 1.3 Setup with PandaPal (6 hours)

**Step 1: Install PandaPal Gem**
```ruby
# Gemfile
gem 'pandapal'
gem 'canvas_sync'
gem 'sidekiq'
gem 'redis'
```

**Step 2: Configure LTI 1.3**
```ruby
# config/initializers/pandapal.rb
PandaPal.configure do |config|
  config.client_id = ENV['CANVAS_CLIENT_ID']
  config.deployment_id = ENV['CANVAS_DEPLOYMENT_ID']
  config.private_key = ENV['LTI_PRIVATE_KEY']
  config.public_key_url = "#{ENV['APP_URL']}/api/lti/public_key"
end
```

**Step 3: Create LTI Controllers**
```ruby
# app/controllers/lti_controller.rb
class LtiController < ApplicationController
  include PandaPal::<PERSON><PERSON><PERSON><PERSON>roller
  
  def launch
    # Handle course_navigation and account_navigation launches
    case launch_type
    when 'course_navigation'
      redirect_to course_dashboard_path(course_id: context_id)
    when 'account_navigation'
      redirect_to account_dashboard_path(account_id: context_id)
    end
  end
end
```

### 1.2 Database Schema Design (3 hours)

**Migration Files:**
```ruby
# db/migrate/001_create_observer_links.rb
class CreateObserverLinks < ActiveRecord::Migration[7.0]
  def change
    create_table :observer_links do |t|
      t.bigint :observer_canvas_id, null: false
      t.bigint :observee_canvas_id, null: false
      t.bigint :canvas_link_id
      t.datetime :expires_at, null: false
      t.datetime :renewed_at
      t.integer :renewal_count, default: 0
      t.string :status, default: 'active'
      t.string :instance_id
      t.bigint :created_by_user_id
      t.timestamps
      
      t.index [:observer_canvas_id, :status]
      t.index :expires_at
      t.index [:instance_id, :created_at]
    end
  end
end
```

### 1.3 React Frontend Setup (4 hours)

**Package.json Dependencies:**
```json
{
  "dependencies": {
    "@instructure/ui": "^8.0.0",
    "@instructure/ui-react-utils": "^8.0.0",
    "react": "^18.3.1",
    "react-dom": "^18.3.1",
    "react-router-dom": "^6.0.0"
  }
}
```

**Main App Component:**
```jsx
// app/javascript/components/App.jsx
import React from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import { InstUISettingsProvider } from '@instructure/ui'
import CourseDashboard from './CourseDashboard'
import AccountDashboard from './AccountDashboard'

export default function App() {
  return (
    <InstUISettingsProvider>
      <Router>
        <Routes>
          <Route path="/course/:courseId" element={<CourseDashboard />} />
          <Route path="/account/:accountId" element={<AccountDashboard />} />
        </Routes>
      </Router>
    </InstUISettingsProvider>
  )
}
```

## Phase 2: Core Observer Functionality (24 hours)

### 2.1 Student List APIs (8 hours)

**Course Level API:**
```ruby
# app/controllers/api/v1/students_controller.rb
class Api::V1::StudentsController < ApplicationController
  def course_students
    course_id = params[:course_id]
    page = params[:page] || 1
    
    enrollments = canvas_api.get_course_enrollments(
      course_id, 
      type: ['StudentEnrollment'],
      per_page: 50,
      page: page
    )
    
    students = enrollments.map do |enrollment|
      {
        id: enrollment['user']['id'],
        name: enrollment['user']['name'],
        sortable_name: enrollment['user']['sortable_name'],
        sis_user_id: enrollment['user']['sis_user_id']
      }
    end.sort_by { |s| s[:sortable_name] }
    
    render json: { students: students, pagination: pagination_info }
  end
end
```

**Account Level API with Search:**
```ruby
def account_students
  account_id = params[:account_id]
  search_term = params[:search]
  
  users = if search_term.present? && search_term.length >= 3
    canvas_api.search_account_users(account_id, search_term)
  else
    canvas_api.get_account_users(account_id, enrollment_type: 'student')
  end
  
  render json: { students: format_students(users) }
end
```

### 2.2 Observer Link Management (10 hours)

**Link Creation Service:**
```ruby
# app/services/observer_link_service.rb
class ObserverLinkService
  def create_temporary_link(observer_id, observee_id, instance_id)
    # Check for existing active links
    existing_link = ObserverLink.active.find_by(observer_canvas_id: observer_id)
    return { error: 'Active link exists' } if existing_link
    
    # Create Canvas observer link
    canvas_response = canvas_api.create_observer_link(observer_id, observee_id)
    
    # Store in database
    link = ObserverLink.create!(
      observer_canvas_id: observer_id,
      observee_canvas_id: observee_id,
      canvas_link_id: canvas_response['id'],
      expires_at: 1.hour.from_now,
      instance_id: instance_id
    )
    
    # Schedule cleanup job
    ObserverLinkCleanupJob.perform_at(link.expires_at, link.id)
    
    { success: true, link: link }
  end
end
```

### 2.3 Frontend Student List Component (6 hours)

**Student List Component:**
```jsx
// app/javascript/components/StudentList.jsx
import React, { useState, useEffect } from 'react'
import { Table } from '@instructure/ui-table'
import { Button } from '@instructure/ui-buttons'
import { Pagination } from '@instructure/ui-pagination'

export default function StudentList({ courseId, onSelectStudent }) {
  const [students, setStudents] = useState([])
  const [currentPage, setCurrentPage] = useState(1)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    fetchStudents(currentPage)
  }, [currentPage])
  
  const fetchStudents = async (page) => {
    setLoading(true)
    const response = await fetch(`/api/v1/courses/${courseId}/students?page=${page}`)
    const data = await response.json()
    setStudents(data.students)
    setLoading(false)
  }
  
  return (
    <div>
      <Table caption="Course Students">
        <Table.Head>
          <Table.Row>
            <Table.ColHeader>Name</Table.ColHeader>
            <Table.ColHeader>SIS ID</Table.ColHeader>
            <Table.ColHeader>Actions</Table.ColHeader>
          </Table.Row>
        </Table.Head>
        <Table.Body>
          {students.map(student => (
            <Table.Row key={student.id}>
              <Table.Cell>{student.sortable_name}</Table.Cell>
              <Table.Cell>{student.sis_user_id}</Table.Cell>
              <Table.Cell>
                <Button onClick={() => onSelectStudent(student)}>
                  Create Observer Link
                </Button>
              </Table.Cell>
            </Table.Row>
          ))}
        </Table.Body>
      </Table>
      
      <Pagination
        variant="compact"
        labelNext="Next Page"
        labelPrev="Previous Page"
      />
    </div>
  )
}
```

## Phase 3: Calendar Integration (12 hours)

### 3.1 Calendar Data API (4 hours)

**Calendar Controller:**
```ruby
# app/controllers/api/v1/calendar_controller.rb
class Api::V1::CalendarController < ApplicationController
  def student_calendar
    link = current_observer_link
    return render json: { error: 'No active link' } unless link
    
    observee_id = link.observee_canvas_id
    
    # Fetch personal calendar
    personal_events = canvas_api.get_user_calendar_events(observee_id)
    
    # Fetch course calendars
    course_calendars = canvas_api.get_user_courses(observee_id).map do |course|
      {
        id: course['id'],
        name: course['name'],
        events: canvas_api.get_course_calendar_events(course['id'])
      }
    end
    
    render json: {
      personal_events: personal_events,
      course_calendars: course_calendars.first(9), # Limit to 9
      student: { name: link.observee_name, sis_id: link.observee_sis_id }
    }
  end
end
```

### 3.2 Calendar UI Component (5 hours)

**Calendar Component:**
```jsx
// app/javascript/components/Calendar.jsx
import React, { useState, useEffect } from 'react'
import { Calendar } from '@instructure/ui-calendar'
import { Checkbox } from '@instructure/ui-checkbox'
import { View } from '@instructure/ui-view'

export default function StudentCalendar({ linkId }) {
  const [calendarData, setCalendarData] = useState(null)
  const [visibleCalendars, setVisibleCalendars] = useState({})
  
  useEffect(() => {
    fetchCalendarData()
  }, [linkId])
  
  const fetchCalendarData = async () => {
    const response = await fetch(`/api/v1/observer_links/${linkId}/calendar`)
    const data = await response.json()
    setCalendarData(data)
    
    // Set default visibility
    const defaultVisibility = {}
    if (data.course_calendars.length <= 9) {
      data.course_calendars.forEach(cal => {
        defaultVisibility[cal.id] = true
      })
    }
    defaultVisibility['personal'] = true
    setVisibleCalendars(defaultVisibility)
  }
  
  const toggleCalendar = (calendarId) => {
    setVisibleCalendars(prev => ({
      ...prev,
      [calendarId]: !prev[calendarId]
    }))
  }
  
  return (
    <View>
      <View margin="medium">
        <h2>Calendar for {calendarData?.student.name}</h2>
        <p>SIS ID: {calendarData?.student.sis_id}</p>
      </View>
      
      <View margin="medium">
        <h3>Calendar Visibility</h3>
        <Checkbox
          label="Personal Calendar"
          checked={visibleCalendars.personal}
          onChange={() => toggleCalendar('personal')}
        />
        {calendarData?.course_calendars.map(calendar => (
          <Checkbox
            key={calendar.id}
            label={calendar.name}
            checked={visibleCalendars[calendar.id]}
            onChange={() => toggleCalendar(calendar.id)}
          />
        ))}
      </View>
      
      <Calendar
        events={getVisibleEvents()}
        readOnly={true}
      />
    </View>
  )
}
```

## Phase 4: Background Jobs (9 hours)

### 4.1 Sidekiq Configuration (2 hours)

**Sidekiq Setup:**
```ruby
# config/initializers/sidekiq.rb
Sidekiq.configure_server do |config|
  config.redis = { url: ENV['REDIS_URL'] }
end

Sidekiq.configure_client do |config|
  config.redis = { url: ENV['REDIS_URL'] }
end
```

### 4.2 Data Sync Jobs (4 hours)

**Canvas Data Sync Job:**
```ruby
# app/jobs/canvas_data_sync_job.rb
class CanvasDataSyncJob < ApplicationJob
  queue_as :default
  
  def perform
    sync_users
    sync_courses
    sync_enrollments
  end
  
  private
  
  def sync_users
    canvas_api.get_all_users.each do |user|
      CanvasUser.find_or_create_by(canvas_id: user['id']) do |cu|
        cu.name = user['name']
        cu.sortable_name = user['sortable_name']
        cu.sis_user_id = user['sis_user_id']
        cu.last_synced_at = Time.current
      end
    end
  end
end
```

### 4.3 Link Cleanup Jobs (3 hours)

**Observer Link Cleanup:**
```ruby
# app/jobs/observer_link_cleanup_job.rb
class ObserverLinkCleanupJob < ApplicationJob
  def perform(link_id)
    link = ObserverLink.find(link_id)
    return unless link.active?
    
    # Remove from Canvas
    canvas_api.delete_observer_link(link.observer_canvas_id, link.observee_canvas_id)
    
    # Update local record
    link.update!(status: 'expired')
    
    # Sync across instances
    broadcast_link_expiration(link)
  end
end
```

This implementation guide provides the detailed technical steps needed to build each component of the LTI application. Each phase builds upon the previous one, ensuring a systematic development approach that can be completed within the 80-hour timeframe.
