class CreateCelebrationEvents < ActiveRecord::Migration[7.0]
  def change
    create_table :celebration_events do |t|
      t.bigint :canvas_user_id, null: false
      t.string :event_type, null: false
      t.bigint :canvas_course_id
      t.bigint :canvas_module_id
      t.datetime :event_date
      t.datetime :shown_at # when the fireworks were actually shown
      t.timestamps
    end
    add_index :celebration_events, [:canvas_user_id, :event_type, :canvas_module_id, :event_date], unique: true, name: 'index_celebration_events_uniqueness'
  end
end
