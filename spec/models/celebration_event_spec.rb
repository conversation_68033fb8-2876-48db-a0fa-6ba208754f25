# frozen_string_literal: true

require 'rails_helper'

RSpec.describe CelebrationEvent, type: :model do
  describe 'factory' do
    it 'is valid with default attributes' do
      event = build(:celebration_event)
      expect(event).to be_valid
    end

    it 'can be created with all required attributes' do
      user = create(:user)
      course = create(:course)
      event = create(:celebration_event,
                     canvas_user_id: user.canvas_id,
                     canvas_course_id: course.canvas_id,
                     event_type: 'module_completed',
                     canvas_module_id: 12_345)
      expect(event).to be_persisted
      expect(event.event_type).to eq('module_completed')
      expect(event.canvas_user_id.to_s).to eq(user.canvas_id.to_s)
      expect(event.canvas_course_id.to_s).to eq(course.canvas_id.to_s)
      expect(event.canvas_module_id).to eq(12_345)
    end
  end
end
