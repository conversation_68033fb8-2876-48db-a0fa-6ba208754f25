﻿Story,Task,Description,Dev Hours,Owner,QA Hours
Milestone 2,,,,,
Initial Setup,Initial Setup of LTI,"Initial setup will include pandapal, canvas sync gem setup and Frontend Setup",8,,
Job Implemention & User Access,Implemention to create job to sync data from canvas to LTI in every six hours. And user access based on SSD.,2,,,
"Course Level Admin and Account Level Users accessing from the Sub-Account Level, accessing from the Solution, to create temporary link so that admin can view the selected student’s courses other stuff.",Accessbility that admin can launch lti at course Level and sub-account level,Implement functionality on Backend and Frontend level that admin can launch lti at course Level and sub-account level.,8,,
Course level student list,"Implement student API to list of all active student enrollments in their course. 
And integrate API to Allow Admin User on course level to view a list of all active student enrollments in their course.
- Student list will be sorted alphabetically(Canvas Sortable Name)
- List will include Student's Canvas Sortable Names and SIS IDs
- The list of students will be retrieved when the Course Level Admin User accesses the launch point and will be 50 records in per page.

",8,,,
Implement Functionality to allow course level user to select a student and establish a temporary Observer/Observee Account Links between the Course Level Admin User and the selected Student.,"-Create schema design and model to save links accounts(Observer/Observee) data in DB.
-Multiple Course Level Admin Users and Account Users can be temporarily linked to the same student, but only a single student can be linked to the Course Level Admin User at a time.
-Once a link has been established, store the timestamp in DB.
-When the link is established, the student list will be hidden.
-Imlement functionlity to allow Admin to currently active link to be ended by the user.
-Once the accounts link will end after one hour or ended by user the all student list will show.

Note: This will include API implemention, DB design and UI implemention
Links accounts data will be be save in db for both Course Level & Sub-account Level Temporary links",12,,,
Implement Functionality to allow Account user accessing from the Sub-Account Level to view a list of all active students within their sub-account.,"-Implement API to show all the active student with at least one active enrollment in a course within their sub-account.
-Implement API for Search Functionality.
-Integrate Search Functionality and list will filtered via Search Functionality.",4,,,
Implement Functionality to allow course level user to select a student and establish a temporary Observer/Observee Account Links between the Course Level Admin User and the selected Student.,"-Multiple Course Level Admin Users and Account Users can be temporarily linked to the same student, but only a single student can be linked to the Course Level Admin User at a time.
-Once a link has been established, store the timestamp in DB.
-When the link is established, the student list will be hidden.
-Imlement functionlity to allow Admin to currently active link to be ended by the user.
-Once the accounts link will end after one hour or ended by user the all student list will show.

Note: This will include API implemention, DB design and UI implemention",8,,,
Job Implemention for Update Account Link model.,"Implement job to update sync process a job that clears any unsuccessful automatic link removals.
",6,,,
"Allow for users currently viewing the currently established link details to renew the link one time. Once renewed, reset the hour timer, and do not remove the link until after this new timer has elapsed.","Implement API to renew accounts links for one time and update timestamp.
Integrate API and UI Implemention to give access to admin to renew link.",6,,,
Milestone 3,,,,,
"Funtionality to implement Canvas Calendar for the selected student within the Solution.
",Student calendar viewing access,"-Implement functionlity to fetch canvas calendar data of temporary accounts link student.
-while accessing Calendar view, display the linked Student’s Name and SIS ID.
-Implement functionality to renow current link(this will include Backend and Frontend implemention)
",8,,
"Calendar View will include the Student’s personal Canvas Calendar and events, as well as up to 9 of the Student’s course and/or group Calendars.
","Implement API to show canvas calendar, events and upto 9 courses or group calendar.
-These calendars can be disabled and enabled as needed. so disable/enable data will store in db.
If the student has 10 or more combined course or group calendars, only the student’s personal calendar will be set to display by default.

Note - The calendar view will match the default Canvas Calendar view, and the view is read-only.
The Find Appoint functionality will not be included.






",16,,,
,,,86,,